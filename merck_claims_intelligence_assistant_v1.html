<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Disease Analyzer - User Flow Mockup</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #FF8C42 0%, #FF6B35 100%);
            color: white;
            padding: 20px 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: white;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #FF8C42;
            font-weight: bold;
        }

        .nav-tabs {
            display: flex;
            gap: 10px;
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 8px;
            margin: 20px 30px 0;
        }

        .nav-tab {
            padding: 8px 16px;
            background: transparent;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-tab.active {
            background: rgba(255,255,255,0.2);
        }

        .content {
            padding: 30px;
        }

        .step {
            display: none;
        }

        .step.active {
            display: block;
        }

        .step-title {
            font-size: 24px;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .step-number {
            background: #FF8C42;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .progress-indicators {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 40px;
        }

        .progress-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            color: #888;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .progress-circle.active {
            background: linear-gradient(135deg, #FF8C42 0%, #FF6B35 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(255,140,66,0.3);
        }

        .progress-line {
            width: 60px;
            height: 2px;
            background: #e0e0e0;
            margin: 0 15px;
            transition: all 0.3s ease;
        }

        .progress-line.active {
            background: linear-gradient(135deg, #FF8C42 0%, #FF6B35 100%);
        }

        .pagination-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }

        .pagination-btn {
            width: 45px;
            height: 45px;
            border: 2px solid #e0e0e0;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            color: #666;
            transition: all 0.3s ease;
        }

        .pagination-btn:hover {
            border-color: #FF8C42;
            color: #FF8C42;
            transform: scale(1.05);
        }

        .pagination-btn:disabled {
            opacity: 0.3;
            cursor: not-allowed;
            transform: none;
        }

        .pagination-btn:disabled:hover {
            border-color: #e0e0e0;
            color: #666;
        }

        .therapy-pagination, .indication-pagination {
            position: relative;
            width: 900px;
            height: 180px;
            overflow: hidden;
            border-radius: 16px;
        }

        .therapy-page, .indication-page {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            gap: 20px;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.4s ease;
        }

        .therapy-page.active, .indication-page.active {
            opacity: 1;
            transform: translateX(0);
        }

        .therapy-page.prev, .indication-page.prev {
            transform: translateX(-100%);
        }

        .continue-btn {
            background: linear-gradient(135deg, #FF8C42 0%, #FF6B35 100%);
            color: white;
            border: none;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 30px auto 0;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(255,140,66,0.3);
        }

        .continue-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 35px rgba(255,140,66,0.4);
        }

        .continue-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 4px 15px rgba(255,140,66,0.2);
        }

        .continue-btn:disabled:hover {
            transform: none;
            box-shadow: 0 4px 15px rgba(255,140,66,0.2);
        }

        /* Code Validation Styles */
        .code-validation-panel {
            background: white;
            border-radius: 18px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.06);
            transition: all 0.4s ease;
            position: relative;
        }

        .code-validation-panel:hover {
            transform: translateY(-4px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.12);
        }

        .panel-header {
            color: white;
            padding: 24px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .panel-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.1);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .code-validation-panel:hover .panel-header::before {
            opacity: 1;
        }

        .panel-header h4 {
            margin: 0;
            font-size: 18px;
            font-weight: 700;
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .selection-badge {
            background: rgba(255,255,255,0.25);
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 13px;
            font-weight: 600;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            position: relative;
            z-index: 2;
        }

        .code-list {
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .code-item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 12px;
            transition: all 0.3s ease;
            position: relative;
        }

        .code-item.selected {
            background: linear-gradient(135deg, #e6fffa 0%, #f0fff4 100%);
            border-color: #38a169;
            box-shadow: 0 4px 12px rgba(56, 161, 105, 0.15);
        }

        .code-item.suggested {
            background: #f7fafc;
            border-color: #cbd5e0;
            opacity: 0.8;
        }

        .code-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.1);
        }

        .code-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .code-number {
            font-family: 'Monaco', 'Menlo', monospace;
            font-weight: 600;
            color: #2d3748;
            background: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 13px;
            border: 1px solid #e2e8f0;
        }

        .code-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .confidence-score {
            font-size: 12px;
            font-weight: 600;
            color: #38a169;
            background: rgba(56, 161, 105, 0.1);
            padding: 4px 8px;
            border-radius: 12px;
        }

        .remove-code-btn {
            background: #fed7d7;
            color: #e53e3e;
            border: none;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .remove-code-btn:hover {
            background: #e53e3e;
            color: white;
            transform: scale(1.1);
        }

        .add-code-btn {
            background: #c6f6d5;
            color: #38a169;
            border: none;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .add-code-btn:hover {
            background: #38a169;
            color: white;
            transform: scale(1.1);
        }

        .code-description {
            color: #4a5568;
            font-size: 14px;
            line-height: 1.4;
            font-weight: 500;
        }

        /* New therapy area icons */
        .oncology-icon {
            background: linear-gradient(135deg, #e53e3e 0%, #fc8181 100%);
        }

        .endocrinology-icon {
            background: linear-gradient(135deg, #805ad5 0%, #b794f6 100%);
        }

        .therapy-card {
            flex: 0 0 260px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 2px solid #e0e0e0;
            border-radius: 16px;
            padding: 25px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .therapy-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #e0e0e0 0%, #e0e0e0 100%);
            transition: all 0.3s ease;
        }

        .therapy-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(255,140,66,0.15);
            border-color: #FF8C42;
        }

        .therapy-card:hover::before {
            background: linear-gradient(135deg, #FF8C42 0%, #FF6B35 100%);
        }

        .therapy-card.selected {
            border-color: #FF8C42;
            background: linear-gradient(135deg, #fff5f0 0%, #ffffff 100%);
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(255,140,66,0.2);
        }

        .therapy-card.selected::before {
            background: linear-gradient(135deg, #FF8C42 0%, #FF6B35 100%);
        }

        .therapy-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            margin: 0 auto 15px;
            transition: all 0.3s ease;
        }

        .psychiatry-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .nephrology-icon {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .neurology-icon {
            background: linear-gradient(135deg, #9f7aea 0%, #ed64a6 100%);
        }

        .rheumatology-icon {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }

        .other-icon {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }

        .therapy-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .therapy-count {
            font-size: 13px;
            color: #666;
            font-weight: 500;
        }

        .indication-card {
            flex: 0 0 180px;
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .indication-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #FF8C42;
        }

        .indication-card.selected {
            border-color: #FF8C42;
            background: linear-gradient(135deg, #fff5f0 0%, #ffffff 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255,140,66,0.15);
        }

        .indication-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            margin: 0 auto 12px;
            transition: all 0.3s ease;
        }

        /* Psychiatry & Neurology indication icons */
        .adhd-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .schizophrenia-icon {
            background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
        }

        .depression-icon {
            background: linear-gradient(135deg, #4c51bf 0%, #5a67d8 100%);
        }

        .bipolar-icon {
            background: linear-gradient(135deg, #553c9a 0%, #4c51bf 100%);
        }

        /* Nephrology indication icons */
        .nephropathy-icon {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .adpkd-icon {
            background: linear-gradient(135deg, #3182ce 0%, #4facfe 100%);
        }

        .ckd-icon {
            background: linear-gradient(135deg, #2b77cb 0%, #3182ce 100%);
        }

        .aki-icon {
            background: linear-gradient(135deg, #1e40af 0%, #2b77cb 100%);
        }

        /* Dermatology indication icons */
        .ad-icon {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .psoriasis-icon {
            background: linear-gradient(135deg, #f093fb 0%, #fa709a 100%);
        }

        .eczema-icon {
            background: linear-gradient(135deg, #e879f9 0%, #f093fb 100%);
        }

        /* Rheumatology indication icons */
        .ra-icon {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }

        .osteoarthritis-icon {
            background: linear-gradient(135deg, #96e6a1 0%, #a8edea 100%);
        }

        .lupus-icon {
            background: linear-gradient(135deg, #84fab0 0%, #96e6a1 100%);
        }

        /* Oncology indication icons */
        .urothelial-cancer-icon {
            background: linear-gradient(135deg, #3182ce 0%, #63b3ed 100%);
        }

        .lung-cancer-icon {
            background: linear-gradient(135deg, #e53e3e 0%, #fc8181 100%);
        }

        .breast-cancer-icon {
            background: linear-gradient(135deg, #d53f8c 0%, #f687b3 100%);
        }

        .melanoma-icon {
            background: linear-gradient(135deg, #dd6b20 0%, #fbd38d 100%);
        }

        /* Neurology indication icons */
        .epilepsy-icon {
            background: linear-gradient(135deg, #9f7aea 0%, #c3a6f0 100%);
        }

        .multiple-sclerosis-icon {
            background: linear-gradient(135deg, #805ad5 0%, #b794f6 100%);
        }

        .alzheimers-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        /* Others indication icons */
        .pku-icon {
            background: linear-gradient(135deg, #38b2ac 0%, #81e6d9 100%);
        }

        .rare-diseases-icon {
            background: linear-gradient(135deg, #805ad5 0%, #d6bcfa 100%);
        }

        .indication-name {
            font-size: 15px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .indication-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        .indication-section {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.4s ease;
        }

        .indication-section.show {
            opacity: 1;
            transform: translateY(0);
        }

        .report-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 4px solid #FF8C42;
        }

        .example-badge {
            background: #dc3545;
            color: white;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .report-title h2 {
            color: #333;
            font-size: 28px;
            font-weight: 700;
            margin: 10px 0;
        }

        .disease-description {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(74, 85, 104, 0.05);
            border-radius: 8px;
            border-left: 3px solid #4a5568;
        }

        .disease-description p {
            color: #555;
            font-size: 14px;
            line-height: 1.6;
            margin: 0;
        }

        .insight-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 25px;
            margin-top: 20px;
        }

        .insight-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border-left: 4px solid #e0e0e0;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .insight-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }

        .insight-card.expanded {
            grid-column: 1 / -1;
        }

        .disease-overview-card {
            border-left-color: #4a5568;
        }

        .market-evolution-card {
            border-left-color: #38a169;
        }

        .patient-insights-card {
            border-left-color: #3182ce;
        }

        .unmet-need-card {
            border-left-color: #d69e2e;
        }

        .competitive-card {
            border-left-color: #e53e3e;
        }

        .insight-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 12px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .insight-header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .insight-icon {
            font-size: 24px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .insight-header h3 {
            color: #333;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .expand-btn {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 6px 12px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .expand-btn:hover {
            background: #e9ecef;
            border-color: #FF8C42;
        }

        .expand-btn.expanded {
            background: #FF8C42;
            color: white;
            border-color: #FF8C42;
        }

        .insight-content {
            color: #555;
            line-height: 1.5;
        }

        .insight-summary {
            display: block;
        }

        .insight-details {
            display: none;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #f0f0f0;
        }

        .insight-details.show {
            display: block;
            animation: fadeInUp 0.3s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .report-filters {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }

        .filter-select {
            padding: 6px 12px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: white;
            font-size: 14px;
            cursor: pointer;
        }

        .filter-select:focus {
            outline: none;
            border-color: #FF8C42;
            box-shadow: 0 0 0 2px rgba(255,140,66,0.2);
        }

        .view-toggle {
            display: flex;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            overflow: hidden;
        }

        .view-toggle-btn {
            padding: 8px 16px;
            background: transparent;
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .view-toggle-btn.active {
            background: #FF8C42;
            color: white;
        }

        .chart-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .chart-tab {
            padding: 10px 20px;
            background: transparent;
            border: none;
            border-bottom: 2px solid transparent;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
        }

        .chart-tab.active {
            color: #FF8C42;
            border-bottom-color: #FF8C42;
        }

        .chart-content {
            display: none;
        }

        .chart-content.active {
            display: block;
        }

        .mini-chart {
            width: 100%;
            height: 150px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
        }

        .mini-chart::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M10,80 Q30,20 50,60 Q70,10 90,50" stroke="white" stroke-width="2" fill="none" opacity="0.3"/></svg>');
            background-size: 100% 100%;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .stat-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .stat-card-value {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }

        .stat-card-label {
            font-size: 12px;
            color: #666;
            line-height: 1.3;
        }

        .interactive-timeline {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .timeline-controls {
            display: flex;
            gap: 10px;
        }

        .timeline-btn {
            padding: 6px 12px;
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .timeline-btn.active {
            background: #FF8C42;
            color: white;
            border-color: #FF8C42;
        }

        .timeline-content {
            height: 200px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
        }

        .export-options {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .export-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102,126,234,0.3);
        }

        .export-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }

        .progress-tracker {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .progress-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .progress-item:last-child {
            border-bottom: none;
        }

        .progress-label {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .progress-bar {
            width: 200px;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #FF8C42 0%, #FF6B35 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .quick-insights {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
        }

        .quick-insights h3 {
            margin-bottom: 15px;
            font-size: 18px;
        }

        .insights-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .insights-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .insight-icon-mini {
            width: 20px;
            height: 20px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .comparison-toggle {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .comparison-options {
            display: flex;
            gap: 15px;
            margin-top: 15px;
        }

        .comparison-card {
            flex: 1;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .comparison-card:hover {
            border-color: #FF8C42;
            background: #fff5f0;
        }

        .comparison-card.selected {
            border-color: #FF8C42;
            background: linear-gradient(135deg, #fff5f0 0%, #ffffff 100%);
        }

        .floating-summary {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
            max-width: 300px;
            z-index: 1000;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }

        .floating-summary.show {
            transform: translateY(0);
        }

        .floating-summary h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }

        .floating-summary p {
            margin: 0;
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }

        .close-floating {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #999;
        }

        /* Sidebar Navigation Styles */
        .report-layout {
            display: flex;
            gap: 0;
            min-height: 500px;
        }

        .sidebar-nav {
            width: 280px;
            background: #ffffff;
            border-radius: 12px 0 0 12px;
            overflow: hidden;
            box-shadow: inset -1px 0 0 #e5e7eb;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 20px;
            background: transparent;
            color: #374151;
            text-decoration: none;
            font-weight: 500;
            font-size: 15px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-item:hover {
            background: #f3f4f6;
            color: #1f2937;
        }

        .nav-item.active {
            background: #1f2937;
            color: white;
            font-weight: 600;
        }

        .nav-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #FF8C42;
        }

        .nav-icon {
            font-size: 18px;
            width: 20px;
            text-align: center;
            flex-shrink: 0;
        }

        .main-content-area {
            flex: 1;
            background: white;
            border-radius: 0 12px 12px 0;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        @media (max-width: 768px) {
            .report-layout {
                flex-direction: column;
            }
            
            .sidebar-nav {
                width: 100%;
                border-radius: 12px 12px 0 0;
            }
            
            .main-content-area {
                border-radius: 0 0 12px 12px;
            }
        }

        .bookmark-btn {
            background: none;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 6px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
        }

        .bookmark-btn:hover {
            border-color: #FF8C42;
            color: #FF8C42;
        }

        .bookmark-btn.bookmarked {
            background: #FF8C42;
            color: white;
            border-color: #FF8C42;
        }

        /* KPI Popup Modal Styles */
        .kpi-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .kpi-modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 12px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .kpi-modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .kpi-modal-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
        }

        .kpi-modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s ease;
        }

        .kpi-modal-close:hover {
            background: rgba(255,255,255,0.2);
        }

        .kpi-modal-body {
            padding: 30px;
        }

        .kpi-section {
            margin-bottom: 25px;
        }

        .kpi-section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .kpi-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            border-left: 4px solid #e0e0e0;
            transition: all 0.3s ease;
        }

        .kpi-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .kpi-value {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }

        .kpi-label {
            font-size: 12px;
            color: #666;
            line-height: 1.3;
        }

        .kpi-description {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }

        .kpi-description p {
            margin: 0;
            color: #555;
            font-size: 14px;
            line-height: 1.5;
        }

        /* Chatbot Modal Styles */
        .chatbot-modal {
            display: none;
            position: fixed;
            z-index: 1001;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

                 .chatbot-modal-content {
             background-color: white;
             margin: 1% auto;
             padding: 0;
             border-radius: 12px;
             width: 95%;
             max-width: 1200px;
             height: 90vh;
             box-shadow: 0 20px 40px rgba(0,0,0,0.15);
             animation: modalSlideIn 0.3s ease;
             display: flex;
             flex-direction: column;
         }

        .chatbot-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chatbot-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chatbot-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s ease;
        }

        .chatbot-close:hover {
            background: rgba(255,255,255,0.2);
        }

        .chatbot-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }

        .message.bot .message-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .message.user .message-avatar {
            background: #FF8C42;
            color: white;
        }

        .message-content {
            background: white;
            padding: 12px 16px;
            border-radius: 18px;
            max-width: 70%;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            line-height: 1.4;
            font-size: 14px;
        }

        .message.user .message-content {
            background: #FF8C42;
            color: white;
        }

        .message-time {
            font-size: 11px;
            color: #999;
            margin-top: 5px;
        }

        .chat-input-area {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .chat-input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #e0e0e0;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .chat-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102,126,234,0.2);
        }

        .chat-send-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .chat-send-btn:hover {
            transform: scale(1.05);
        }

        .chat-send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: none;
            padding: 10px 16px;
            background: white;
            border-radius: 18px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 15px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #ccc;
            border-radius: 50%;
            animation: typingAnimation 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typingAnimation {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .suggested-questions {
            padding: 0 20px 15px;
            background: #f8f9fa;
        }

        .suggested-questions h4 {
            margin: 0 0 10px 0;
            font-size: 13px;
            color: #666;
            font-weight: 500;
        }

        .suggestion-chip {
            display: inline-block;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 15px;
            padding: 6px 12px;
            margin: 3px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

                 .suggestion-chip:hover {
             border-color: #667eea;
             background: #f0f4f8;
         }


    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <div class="logo-icon">DA</div>
                <div>
                    <h1 style="font-size: 24px;">Claims Intelligence Assistant</h1>
                    <p style="opacity: 0.9; font-size: 14px;">AI-Powered Claims Analytics Data Platform</p>
                </div>
            </div>
            <div style="display: flex; align-items: center; gap: 15px;">
                <button id="chatbotBtn" class="export-btn" onclick="openChatbot()" style="margin: 0; opacity: 0.5; cursor: not-allowed;" disabled>
                    🤖 Ask Claims Analytics Intelligence
                </button>
            </div>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showStep(1)">1. Therapy Selection</button>
            <button class="nav-tab" onclick="showStep(2)">2. Code Validation</button>
            <button class="nav-tab" onclick="showStep(3)">3. Insight Report</button>
        </div>



        <div class="content">
            <!-- Step 1: Therapy and Indication Selection -->
            <div class="step active" id="step1">
                <!-- Progress Indicators -->
                <div class="progress-indicators">
                    <div class="progress-circle" id="therapyCircle">
                        <span>1</span>
                    </div>
                    <div class="progress-line"></div>
                    <div class="progress-circle" id="indicationCircle">
                        <span>2</span>
                    </div>
                </div>
                
                <!-- Therapy Area Selection -->
                <div class="therapy-section">
                    <h4 style="color: #333; margin-bottom: 15px; font-size: 18px;">Select Therapy Area</h4>
                    <div class="pagination-container">
                        <button class="pagination-btn prev-btn" id="therapyPrevBtn" onclick="changeTherapyPage(-1)">
                            <span>‹</span>
                        </button>
                        <div class="therapy-pagination">
                            <div class="therapy-page active" id="therapyPage1">
                                <div class="therapy-card" data-therapy="oncology" onclick="selectTherapy('oncology')">
                                    <div class="therapy-icon oncology-icon">🎗️</div>
                                    <div class="therapy-name">Oncology</div>
                                    <div class="therapy-count">4 indications</div>
                                </div>
                                
                                <div class="therapy-card" data-therapy="rheumatology" onclick="selectTherapy('rheumatology')">
                                    <div class="therapy-icon rheumatology-icon">🦴</div>
                                    <div class="therapy-name">Rheumatology</div>
                                    <div class="therapy-count">3 indications</div>
                                </div>
                                
                                <div class="therapy-card" data-therapy="psychiatry" onclick="selectTherapy('psychiatry')">
                                    <div class="therapy-icon psychiatry-icon">🧠</div>
                                    <div class="therapy-name">Psychiatry</div>
                                    <div class="therapy-count">3 indications</div>
                                </div>
                            </div>
                            
                            <div class="therapy-page" id="therapyPage2">
                                <div class="therapy-card" data-therapy="neurology" onclick="selectTherapy('neurology')">
                                    <div class="therapy-icon neurology-icon">🧬</div>
                                    <div class="therapy-name">Neurology</div>
                                    <div class="therapy-count">3 indications</div>
                                </div>
                                
                                <div class="therapy-card" data-therapy="nephrology" onclick="selectTherapy('nephrology')">
                                    <div class="therapy-icon nephrology-icon">🫘</div>
                                    <div class="therapy-name">Nephrology</div>
                                    <div class="therapy-count">3 indications</div>
                                </div>
                                
                                <div class="therapy-card" data-therapy="others" onclick="selectTherapy('others')">
                                    <div class="therapy-icon other-icon">📋</div>
                                    <div class="therapy-name">Others</div>
                                    <div class="therapy-count">2 indications</div>
                                </div>
                            </div>
                        </div>
                        <button class="pagination-btn next-btn" id="therapyNextBtn" onclick="changeTherapyPage(1)">
                            <span>›</span>
                        </button>
                    </div>
                </div>

                <!-- Indication Selection -->
                <div class="indication-section" id="indicationSection" style="display: none;">
                    <h4 style="color: #333; margin-bottom: 15px; font-size: 18px;">Select Indication</h4>
                    <div class="pagination-container">
                        <button class="pagination-btn prev-btn" id="indicationPrevBtn" onclick="changeIndicationPage(-1)">
                            <span>‹</span>
                        </button>
                        <div class="indication-pagination" id="indicationPagination">
                            <!-- Indication pages will be populated dynamically -->
                        </div>
                        <button class="pagination-btn next-btn" id="indicationNextBtn" onclick="changeIndicationPage(1)">
                            <span>›</span>
                        </button>
                    </div>
                </div>

                <button class="continue-btn" id="continueBtn1" onclick="showStep(2)" disabled>
                    💡
                </button>
            </div>

            <!-- Step 2: Code Validation -->
            <div class="step" id="step2">
                <div class="step-title">
                    <div class="step-number"><b><i>2</i></b></div>
                    <span><b><i>Review and Validate Smart Market Basket</i></b></span>
                    </div>

                                <!-- Simplified Header Section -->
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 16px; padding: 25px; margin-bottom: 30px; color: white;">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 10px;">
                                <span style="font-size: 20px;">🤖</span>
                            </div>
                            <div>
                                <h2 style="margin: 0; font-size: 22px; font-weight: 700; color: white;">
                                    Smart Market Basket Generator
                                </h2>
                                <p style="margin: 3px 0 0 0; font-size: 14px; color: rgba(255,255,255,0.9);">
                                    AI-validated codes for <span id="selectedIndicationDisplay">Urothelial Cancer</span>
                                </p>
                            </div>
                        </div>
                        
                        <div style="text-align: center;">
                            <div style="background: rgba(255,255,255,0.2); border-radius: 10px; padding: 12px 16px;">
                                <div style="font-size: 24px; font-weight: 700; color: white; margin-bottom: 3px;">7</div>
                                <div style="font-size: 11px; color: rgba(255,255,255,0.9); text-transform: uppercase; letter-spacing: 1px;">Selected</div>
                            </div>
                        </div>
                    </div>
                    
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 25px; margin-bottom: 40px;">
                    <!-- Diagnosis Codes Panel -->
                    <div class="code-validation-panel">
                        <div class="panel-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <h4>🩺 ICD-10 Diagnosis Codes</h4>
                            <div class="selection-badge">3 selected</div>
                        </div>
                        <div class="code-list">
                            <div class="code-item selected" data-confidence="98.5">
                                <div class="code-header">
                                    <span class="code-number">C67.9</span>
                                    <div class="code-actions">
                                        <span class="confidence-score">98.5%</span>
                                        <button class="remove-code-btn" onclick="removeCode(this)">×</button>
                                    </div>
                                </div>
                                <div class="code-description">Malignant neoplasm of bladder, unspecified</div>
                            </div>

                            <div class="code-item selected" data-confidence="94.2">
                                <div class="code-header">
                                    <span class="code-number">C67.8</span>
                                    <div class="code-actions">
                                        <span class="confidence-score">94.2%</span>
                                        <button class="remove-code-btn" onclick="removeCode(this)">×</button>
                                    </div>
                                </div>
                                <div class="code-description">Malignant neoplasm of overlapping sites</div>
                            </div>
                            
                            <div class="code-item selected" data-confidence="91.8">
                                <div class="code-header">
                                    <span class="code-number">C67.1</span>
                                    <div class="code-actions">
                                        <span class="confidence-score">91.8%</span>
                                        <button class="remove-code-btn" onclick="removeCode(this)">×</button>
                            </div>
                                </div>
                                <div class="code-description">Malignant neoplasm of dome of bladder</div>
                                </div>
                            
                            <div class="code-item suggested" data-confidence="87.4">
                                <div class="code-header">
                                    <span class="code-number">C67.2</span>
                                    <div class="code-actions">
                                        <span class="confidence-score">87.4%</span>
                                        <button class="add-code-btn" onclick="addCode(this)">+</button>
                                </div>
                                </div>
                                <div class="code-description">Malignant neoplasm of lateral wall of bladder</div>
                                </div>
                                </div>
                            </div>

                    <!-- Drug Codes Panel -->
                    <div class="code-validation-panel">
                        <div class="panel-header" style="background: linear-gradient(135deg, #38a169 0%, #48bb78 100%);">
                            <h4>💊 NDC Drug Codes</h4>
                            <div class="selection-badge">2 selected</div>
                        </div>
                        <div class="code-list">
                            <div class="code-item selected" data-confidence="96.7">
                                <div class="code-header">
                                    <span class="code-number">0003-0280</span>
                                    <div class="code-actions">
                                        <span class="confidence-score">96.7%</span>
                                        <button class="remove-code-btn" onclick="removeCode(this)">×</button>
                                    </div>
                                </div>
                                <div class="code-description">Pembrolizumab injection</div>
                        </div>

                            <div class="code-item selected" data-confidence="94.1">
                                <div class="code-header">
                                    <span class="code-number">0069-0120</span>
                                    <div class="code-actions">
                                        <span class="confidence-score">94.1%</span>
                                        <button class="remove-code-btn" onclick="removeCode(this)">×</button>
                                    </div>
                                </div>
                                <div class="code-description">Atezolizumab injection</div>
                            </div>
                            
                            <div class="code-item suggested" data-confidence="89.3">
                                <div class="code-header">
                                    <span class="code-number">0003-3291</span>
                                    <div class="code-actions">
                                        <span class="confidence-score">89.3%</span>
                                        <button class="add-code-btn" onclick="addCode(this)">+</button>
                                    </div>
                                </div>
                                <div class="code-description">Cisplatin injection</div>
                            </div>

                            <div class="code-item suggested" data-confidence="88.9">
                                <div class="code-header">
                                    <span class="code-number">0069-3112</span>
                                    <div class="code-actions">
                                        <span class="confidence-score">88.9%</span>
                                        <button class="add-code-btn" onclick="addCode(this)">+</button>
                                    </div>
                                    </div>
                                <div class="code-description">Avelumab injection</div>
                                    </div>
                                    </div>
                                </div>

                    <!-- Procedure Codes Panel -->
                    <div class="code-validation-panel">
                        <div class="panel-header" style="background: linear-gradient(135deg, #d69e2e 0%, #f6ad55 100%);">
                            <h4>🔬 CPT Procedure Codes</h4>
                            <div class="selection-badge">2 selected</div>
                        </div>
                        <div class="code-list">
                            <div class="code-item selected" data-confidence="97.2">
                                <div class="code-header">
                                    <span class="code-number">52234</span>
                                    <div class="code-actions">
                                        <span class="confidence-score">97.2%</span>
                                        <button class="remove-code-btn" onclick="removeCode(this)">×</button>
                                    </div>
                                </div>
                                <div class="code-description">Cystourethroscopy with fulguration</div>
                            </div>

                            <div class="code-item selected" data-confidence="93.8">
                                <div class="code-header">
                                    <span class="code-number">51590</span>
                                    <div class="code-actions">
                                        <span class="confidence-score">93.8%</span>
                                        <button class="remove-code-btn" onclick="removeCode(this)">×</button>
                                    </div>
                                    </div>
                                <div class="code-description">Cystectomy, complete</div>
                                    </div>
                            
                            <div class="code-item suggested" data-confidence="91.5">
                                <div class="code-header">
                                    <span class="code-number">96413</span>
                                    <div class="code-actions">
                                        <span class="confidence-score">91.5%</span>
                                        <button class="add-code-btn" onclick="addCode(this)">+</button>
                                    </div>
                                </div>
                                <div class="code-description">Chemotherapy administration, IV</div>
                            </div>

                            <div class="code-item suggested" data-confidence="89.6">
                                <div class="code-header">
                                    <span class="code-number">52235</span>
                                    <div class="code-actions">
                                        <span class="confidence-score">89.6%</span>
                                        <button class="add-code-btn" onclick="addCode(this)">+</button>
                                    </div>
                                    </div>
                                <div class="code-description">Cystourethroscopy with destruction</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                <div style="display: flex; justify-content: center; align-items: center; gap: 20px; margin-top: 40px;">
                    <div style="text-align: center;">
                        <button class="continue-btn" id="continueBtn2" onclick="showStep(3)" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); width: 70px; height: 70px; padding: 0; font-size: 32px; font-weight: 700; border-radius: 50%; box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3); display: flex; align-items: center; justify-content: center; border: none;">🚀</button>
                        <p style="margin: 12px 0 0 0; color: #64748b; font-size: 13px; font-weight: 500;">
                            Proceed with AI-validated market basket
                        </p>
                    </div>
                </div>
                            </div>
                            
            <!-- Step 3: Enhanced Insight Report -->
            <div class="step" id="step3">
                <div class="report-layout">
                    <!-- Sidebar Navigation -->
                    <div class="sidebar-nav">
                        <a href="#" class="nav-item active" onclick="showSection('patient-universe', event)">
                            <span class="nav-icon">👥</span>
                            <span>Patient Universe</span>
                        </a>
                        <a href="#" class="nav-item" onclick="showSection('provider-ecosystem', event)">
                            <span class="nav-icon">🏥</span>
                            <span>Provider Ecosystem</span>
                        </a>
                        <a href="#" class="nav-item" onclick="showSection('treatment-patterns', event)">
                            <span class="nav-icon">💊</span>
                            <span>Treatment Patterns</span>
                        </a>
                        <a href="#" class="nav-item" onclick="showSection('patient-complexity', event)">
                            <span class="nav-icon">🧬</span>
                            <span>Patient Complexity</span>
                        </a>
                    </div>

                    <!-- Main Content Area -->
                    <div class="main-content-area">
                        <!-- Patient Universe Section (Default) -->
                        <div class="content-section active" id="patient-universe-section">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
                                <h2 style="color: #333; font-size: 28px; font-weight: 700; margin: 0;">Patient Universe</h2>
                            </div>

                            <!-- Key Claims-Based Patient Statistics -->
                            <div style="margin-bottom: 30px;">
                                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px;">
                                    <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin: 0; display: flex; align-items: center; gap: 8px;">
                                        📊 Key Claims-Based Patient Statistics
                                    </h3>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <label style="font-size: 13px; color: #64748b; font-weight: 500;">Time Period:</label>
                                        <select style="border: 1px solid #e2e8f0; border-radius: 6px; padding: 6px 12px; font-size: 13px; background: white;">
                                            <option value="12m">Last 12 Months</option>
                                            <option value="24m" selected>Last 24 Months</option>
                                            <option value="36m">Last 36 Months</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="stats-grid">
                                    <div class="stat-card" style="border-left-color: #3182ce;">
                                        <div class="stat-card-value" title="Total number of unique patients with at least one ICD-10 diagnosis code for urothelial cancer in the selected time period">847K</div>
                                        <div class="stat-card-label" title="Patients identified through ICD-10 diagnosis codes (C67.x series) indicating urothelial cancer diagnosis">Patients with Qualifying ICD Codes (Diagnosed)</div>
                                    </div>
                                    <div class="stat-card" style="border-left-color: #38a169;">
                                        <div class="stat-card-value" title="Total number of unique patients with at least one NDC prescription for urothelial cancer treatments in the selected time period">623K</div>
                                        <div class="stat-card-label" title="Patients identified through NDC drug codes for urothelial cancer treatments including chemotherapy and immunotherapy">Patients with Qualifying NDC Codes (Treated)</div>
                                    </div>
                                    <div class="stat-card" style="border-left-color: #d69e2e;">
                                        <div class="stat-card-value" title="Patients with at least one medical or pharmacy claim in the most recent 12-month period, indicating ongoing care">458K</div>
                                        <div class="stat-card-label" title="Active patients are those with recent medical activity, indicating current engagement with healthcare system">Active Patients (Claims in Last 12 Months)</div>
                                    </div>
                                    <div class="stat-card" style="border-left-color: #805ad5;">
                                        <div class="stat-card-value" title="Year-over-year percentage change in total patient volume, calculated by comparing current period to same period in previous year">+12.3%</div>
                                        <div class="stat-card-label" title="Growth rate showing trend in patient volume, positive indicating increasing prevalence or improved diagnosis">Year-on-Year Patient Volume Growth</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Patient Universe Growth Trends with Charts -->
                            <div style="margin-bottom: 25px;">
                                <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin-bottom: 20px; display: flex; align-items: center; gap: 8px;">
                                    📈 Patient Universe Growth Trends
                                </h3>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                                    
                                    <!-- Diagnosis Trends - Line Chart -->
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                            <div style="font-weight: 600; color: #2d3748;" title="Tracks monthly new diagnosis rates showing seasonal patterns and growth trends in urothelial cancer detection">📊 Diagnosis Trends</div>
                                            <select style="border: 1px solid #e2e8f0; border-radius: 4px; padding: 4px 8px; font-size: 12px;">
                                                <option>Monthly</option>
                                                <option>Quarterly</option>
                                                <option>Yearly</option>
                                            </select>
                                        </div>
                                        <!-- Simulated Line Chart -->
                                        <div style="position: relative; height: 120px; background: linear-gradient(to top, #f8fafc 0%, transparent 100%); border-radius: 6px; margin-bottom: 10px;">
                                            <svg width="100%" height="100%" style="position: absolute; top: 0; left: 0;">
                                                <polyline points="10,100 30,85 50,75 70,65 90,50 110,45 130,40 150,35 170,30 190,25 210,22 230,20" 
                                                    fill="none" stroke="#3182ce" stroke-width="3" stroke-linecap="round"/>
                                                <circle cx="230" cy="20" r="4" fill="#3182ce"/>
                                            </svg>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; font-size: 11px; color: #94a3b8; margin-bottom: 10px;">
                                            <span>Jan '23</span>
                                            <span>Dec '24</span>
                                        </div>
                                        <div style="text-align: center;">
                                            <div style="font-size: 24px; font-weight: 700; color: #3182ce; margin-bottom: 5px;">+8.7%</div>
                                            <div style="font-size: 13px; color: #718096;">YoY increase in new diagnoses</div>
                                        </div>
                                    </div>

                                    <!-- Treatment Adoption - Bar Chart -->
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                            <div style="font-weight: 600; color: #2d3748;" title="Monthly treatment initiation rates showing adoption patterns of different therapy lines and drug classes">💊 Treatment Adoption</div>
                                            <select style="border: 1px solid #e2e8f0; border-radius: 4px; padding: 4px 8px; font-size: 12px;">
                                                <option>By Therapy Line</option>
                                                <option>By Drug Class</option>
                                                <option>Total Volume</option>
                                            </select>
                                        </div>
                                        <!-- Simulated Bar Chart -->
                                        <div style="display: flex; align-items: end; height: 120px; gap: 8px; margin-bottom: 10px;">
                                            <div style="background: linear-gradient(to top, #38a169, #48bb78); width: 20px; height: 60%; border-radius: 2px 2px 0 0;"></div>
                                            <div style="background: linear-gradient(to top, #38a169, #48bb78); width: 20px; height: 70%; border-radius: 2px 2px 0 0;"></div>
                                            <div style="background: linear-gradient(to top, #38a169, #48bb78); width: 20px; height: 85%; border-radius: 2px 2px 0 0;"></div>
                                            <div style="background: linear-gradient(to top, #38a169, #48bb78); width: 20px; height: 90%; border-radius: 2px 2px 0 0;"></div>
                                            <div style="background: linear-gradient(to top, #38a169, #48bb78); width: 20px; height: 100%; border-radius: 2px 2px 0 0;"></div>
                                            <div style="background: linear-gradient(to top, #38a169, #48bb78); width: 20px; height: 95%; border-radius: 2px 2px 0 0;"></div>
                                            <div style="background: linear-gradient(to top, #38a169, #48bb78); width: 20px; height: 85%; border-radius: 2px 2px 0 0;"></div>
                                            <div style="background: linear-gradient(to top, #38a169, #48bb78); width: 20px; height: 80%; border-radius: 2px 2px 0 0;"></div>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; font-size: 11px; color: #94a3b8; margin-bottom: 10px;">
                                            <span>Q1</span>
                                            <span>Q4</span>
                                        </div>
                                        <div style="text-align: center;">
                                            <div style="font-size: 24px; font-weight: 700; color: #38a169; margin-bottom: 5px;">+15.2%</div>
                                            <div style="font-size: 13px; color: #718096;">YoY increase in treated patients</div>
                                        </div>
                                    </div>

                                    <!-- Patient Retention - Donut Chart -->
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                            <div style="font-weight: 600; color: #2d3748;" title="Percentage of patients who remain on treatment at various time intervals, showing therapy persistence rates">🔄 Patient Retention</div>
                                            <select style="border: 1px solid #e2e8f0; border-radius: 4px; padding: 4px 8px; font-size: 12px;">
                                                <option>6 Months</option>
                                                <option selected>12 Months</option>
                                                <option>24 Months</option>
                                            </select>
                                        </div>
                                        <!-- Simulated Donut Chart -->
                                        <div style="display: flex; justify-content: center; margin-bottom: 15px;">
                                            <div style="position: relative; width: 100px; height: 100px;">
                                                <svg width="100" height="100" style="transform: rotate(-90deg);">
                                                    <circle cx="50" cy="50" r="35" fill="none" stroke="#f1f5f9" stroke-width="10"/>
                                                    <circle cx="50" cy="50" r="35" fill="none" stroke="#d69e2e" stroke-width="10" 
                                                        stroke-dasharray="161.06" stroke-dashoffset="42.68" stroke-linecap="round"/>
                                                </svg>
                                                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                                                    <div style="font-size: 18px; font-weight: 700; color: #d69e2e;">73.4%</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div style="text-align: center;">
                                            <div style="font-size: 13px; color: #718096;">12-month retention rate</div>
                                        </div>
                                    </div>

                                    <!-- New Patient Share - Pie Chart -->
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                            <div style="font-weight: 600; color: #2d3748;" title="Proportion of treatment-naive patients versus those switching from other therapies, segmented by therapy line and prior treatments">🆕 New Patient Share</div>
                                            <select style="border: 1px solid #e2e8f0; border-radius: 4px; padding: 4px 8px; font-size: 12px;">
                                                <option>1L Therapy</option>
                                                <option>2L Therapy</option>
                                                <option selected>All Lines</option>
                                            </select>
                                        </div>
                                        <!-- Simulated Pie Chart -->
                                        <div style="display: flex; justify-content: center; margin-bottom: 15px;">
                                            <div style="position: relative; width: 100px; height: 100px;">
                                                <svg width="100" height="100" style="transform: rotate(-90deg);">
                                                    <circle cx="50" cy="50" r="35" fill="none" stroke="#cbd5e0" stroke-width="10"/>
                                                    <circle cx="50" cy="50" r="35" fill="none" stroke="#805ad5" stroke-width="10" 
                                                        stroke-dasharray="62.83" stroke-dashoffset="156.07" stroke-linecap="round"/>
                                                </svg>
                                                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                                                    <div style="font-size: 18px; font-weight: 700; color: #805ad5;">28.6%</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div style="text-align: center;">
                                            <div style="font-size: 13px; color: #718096;">Proportion of newly treated patients</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Provider Ecosystem Section -->
                        <div class="content-section" id="provider-ecosystem-section">
                            <div style="margin-bottom: 25px;">
                                <h2 style="color: #333; font-size: 28px; font-weight: 700; margin: 0;">Provider Ecosystem</h2>
                            </div>
                            
                            <!-- Provider Summary -->
                            <div style="background: rgba(56, 161, 105, 0.1); border-radius: 8px; padding: 20px; margin-bottom: 25px; border-left: 4px solid #38a169;">
                                <h3 style="color: #2d3748; font-size: 16px; font-weight: 600; margin-bottom: 10px;">🏥 Provider Network Analysis</h3>
                                <p style="margin: 0; color: #4a5568; font-size: 14px; line-height: 1.6;">
                                    Comprehensive analysis of healthcare providers prescribing within the indication, based on NPI taxonomy codes and prescription volume patterns from claims data.
                                </p>
                            </div>

                            <!-- Top Prescribing Specialties - Bar Chart -->
                            <div style="margin-bottom: 30px;">
                                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px;">
                                    <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin: 0; display: flex; align-items: center; gap: 8px;">
                                        🩺 Top Prescribing Specialties (NPI Taxonomy)
                                    </h3>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <label style="font-size: 13px; color: #64748b; font-weight: 500;">Time Period:</label>
                                        <select style="border: 1px solid #e2e8f0; border-radius: 6px; padding: 6px 12px; font-size: 13px; background: white;">
                                            <option value="12m">Last 12 Months</option>
                                            <option value="24m" selected>Last 24 Months</option>
                                            <option value="36m">Last 36 Months</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 25px;">
                                    <!-- Horizontal Bar Chart for Specialties -->
                                    <div style="display: grid; gap: 20px;">
                                        <div style="display: flex; align-items: center; gap: 15px;">
                                            <div style="min-width: 120px; font-weight: 600; color: #2d3748; font-size: 14px;">🎗️ Medical Oncology</div>
                                            <div style="flex: 1; position: relative; height: 30px; background: #f1f5f9; border-radius: 15px; overflow: hidden;">
                                                <div style="height: 100%; width: 73%; background: linear-gradient(to right, #3182ce, #63b3ed); border-radius: 15px; display: flex; align-items: center; justify-content: end; padding-right: 12px;">
                                                    <span style="color: white; font-weight: 600; font-size: 12px;">9,387 NPIs</span>
                                                </div>
                                                <div style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); font-size: 12px; color: #64748b; font-weight: 500;">73%</div>
                                            </div>
                                        </div>
                                        
                                        <div style="display: flex; align-items: center; gap: 15px;">
                                            <div style="min-width: 120px; font-weight: 600; color: #2d3748; font-size: 14px;">🫘 Urology</div>
                                            <div style="flex: 1; position: relative; height: 30px; background: #f1f5f9; border-radius: 15px; overflow: hidden;">
                                                <div style="height: 100%; width: 18%; background: linear-gradient(to right, #38a169, #68d391); border-radius: 15px; display: flex; align-items: center; justify-content: end; padding-right: 12px;">
                                                    <span style="color: white; font-weight: 600; font-size: 12px;">2,312 NPIs</span>
                                                </div>
                                                <div style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); font-size: 12px; color: #64748b; font-weight: 500;">18%</div>
                                            </div>
                                        </div>
                                        
                                        <div style="display: flex; align-items: center; gap: 15px;">
                                            <div style="min-width: 120px; font-weight: 600; color: #2d3748; font-size: 14px;">🏥 Internal Medicine</div>
                                            <div style="flex: 1; position: relative; height: 30px; background: #f1f5f9; border-radius: 15px; overflow: hidden;">
                                                <div style="height: 100%; width: 6%; background: linear-gradient(to right, #d69e2e, #f6ad55); border-radius: 15px; display: flex; align-items: center; justify-content: end; padding-right: 12px;">
                                                    <span style="color: white; font-weight: 600; font-size: 12px;">771 NPIs</span>
                                                </div>
                                                <div style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); font-size: 12px; color: #64748b; font-weight: 500;">6%</div>
                                            </div>
                                        </div>
                                        
                                        <div style="display: flex; align-items: center; gap: 15px;">
                                            <div style="min-width: 120px; font-weight: 600; color: #2d3748; font-size: 14px;">🩻 Radiation Oncology</div>
                                            <div style="flex: 1; position: relative; height: 30px; background: #f1f5f9; border-radius: 15px; overflow: hidden;">
                                                <div style="height: 100%; width: 3%; background: linear-gradient(to right, #805ad5, #b794f6); border-radius: 15px; display: flex; align-items: center; justify-content: end; padding-right: 12px;">
                                                    <span style="color: white; font-weight: 600; font-size: 12px;">377 NPIs</span>
                                                </div>
                                                <div style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); font-size: 12px; color: #64748b; font-weight: 500;">3%</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Top Geographic Concentration - Bar Chart -->
                            <div style="margin-bottom: 30px;">
                                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px;">
                                    <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin: 0; display: flex; align-items: center; gap: 8px;">
                                        🌎 Top Geographic Concentration
                                    </h3>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <label style="font-size: 13px; color: #64748b; font-weight: 500;">Time Period:</label>
                                        <select style="border: 1px solid #e2e8f0; border-radius: 6px; padding: 6px 12px; font-size: 13px; background: white;">
                                            <option value="12m">Last 12 Months</option>
                                            <option value="24m" selected>Last 24 Months</option>
                                            <option value="36m">Last 36 Months</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 25px;">
                                    <!-- Horizontal Bar Chart for Geographic Concentration -->
                                    <div style="display: grid; gap: 20px;">
                                        <div style="display: flex; align-items: center; gap: 15px;">
                                            <div style="min-width: 80px; font-weight: 600; color: #2d3748; font-size: 14px;">🏙️ California</div>
                                            <div style="flex: 1; position: relative; height: 30px; background: #f1f5f9; border-radius: 15px; overflow: hidden;">
                                                <div style="height: 100%; width: 16.8%; background: linear-gradient(to right, #3182ce, #63b3ed); border-radius: 15px; display: flex; align-items: center; justify-content: end; padding-right: 12px;">
                                                    <span style="color: white; font-weight: 600; font-size: 12px;">2,156 NPIs</span>
                                                </div>
                                                <div style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); font-size: 12px; color: #64748b; font-weight: 500;">16.8%</div>
                                            </div>
                                        </div>
                                        
                                        <div style="display: flex; align-items: center; gap: 15px;">
                                            <div style="min-width: 80px; font-weight: 600; color: #2d3748; font-size: 14px;">🍎 New York</div>
                                            <div style="flex: 1; position: relative; height: 30px; background: #f1f5f9; border-radius: 15px; overflow: hidden;">
                                                <div style="height: 100%; width: 12%; background: linear-gradient(to right, #38a169, #68d391); border-radius: 15px; display: flex; align-items: center; justify-content: end; padding-right: 12px;">
                                                    <span style="color: white; font-weight: 600; font-size: 12px;">1,543 NPIs</span>
                                                </div>
                                                <div style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); font-size: 12px; color: #64748b; font-weight: 500;">12.0%</div>
                                            </div>
                                        </div>
                                        
                                        <div style="display: flex; align-items: center; gap: 15px;">
                                            <div style="min-width: 80px; font-weight: 600; color: #2d3748; font-size: 14px;">🏛️ Texas</div>
                                            <div style="flex: 1; position: relative; height: 30px; background: #f1f5f9; border-radius: 15px; overflow: hidden;">
                                                <div style="height: 100%; width: 10%; background: linear-gradient(to right, #d69e2e, #f6ad55); border-radius: 15px; display: flex; align-items: center; justify-content: end; padding-right: 12px;">
                                                    <span style="color: white; font-weight: 600; font-size: 12px;">1,284 NPIs</span>
                                                </div>
                                                <div style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); font-size: 12px; color: #64748b; font-weight: 500;">10.0%</div>
                                            </div>
                                        </div>
                                        
                                        <div style="display: flex; align-items: center; gap: 15px;">
                                            <div style="min-width: 80px; font-weight: 600; color: #2d3748; font-size: 14px;">🌴 Florida</div>
                                            <div style="flex: 1; position: relative; height: 30px; background: #f1f5f9; border-radius: 15px; overflow: hidden;">
                                                <div style="height: 100%; width: 7.5%; background: linear-gradient(to right, #805ad5, #b794f6); border-radius: 15px; display: flex; align-items: center; justify-content: end; padding-right: 12px;">
                                                    <span style="color: white; font-weight: 600; font-size: 12px;">967 NPIs</span>
                                                </div>
                                                <div style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); font-size: 12px; color: #64748b; font-weight: 500;">7.5%</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Prescription Volume Patterns - Time Charts -->
                            <div style="margin-bottom: 25px;">
                                <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin-bottom: 20px; display: flex; align-items: center; gap: 8px;">
                                    📊 Prescription Volume Patterns
                                </h3>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                                    
                                    <!-- Total Rx Volume - Time Chart -->
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                            <div style="font-weight: 600; color: #2d3748;" title="Monthly prescription volume trends showing seasonal patterns and overall growth in prescription activity">📈 Total Rx Volume Trends</div>
                                            <select style="border: 1px solid #e2e8f0; border-radius: 4px; padding: 4px 8px; font-size: 12px;">
                                                <option>Monthly</option>
                                                <option>Quarterly</option>
                                                <option selected>24 Months</option>
                                            </select>
                                        </div>
                                        <!-- Simulated Area Chart -->
                                        <div style="position: relative; height: 120px; background: linear-gradient(to top, #e6f3ff 0%, transparent 100%); border-radius: 6px; margin-bottom: 10px;">
                                            <svg width="100%" height="100%" style="position: absolute; top: 0; left: 0;">
                                                <defs>
                                                    <linearGradient id="volumeGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                                        <stop offset="0%" style="stop-color:#3182ce;stop-opacity:0.3" />
                                                        <stop offset="100%" style="stop-color:#3182ce;stop-opacity:0.05" />
                                                    </linearGradient>
                                                </defs>
                                                <polygon points="10,90 30,85 50,80 70,75 90,65 110,60 130,55 150,50 170,45 190,40 210,35 230,30 230,120 10,120" 
                                                    fill="url(#volumeGradient)"/>
                                                <polyline points="10,90 30,85 50,80 70,75 90,65 110,60 130,55 150,50 170,45 190,40 210,35 230,30" 
                                                    fill="none" stroke="#3182ce" stroke-width="3" stroke-linecap="round"/>
                                            </svg>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; font-size: 11px; color: #94a3b8; margin-bottom: 10px;">
                                            <span>Jan '23</span>
                                            <span>Dec '24</span>
                                        </div>
                                        <div style="text-align: center;">
                                            <div style="font-size: 18px; font-weight: 700; color: #3182ce; margin-bottom: 5px;">1.25M</div>
                                            <div style="font-size: 13px; color: #718096;">Total prescriptions (24 months)</div>
                                        </div>
                                    </div>

                                    <!-- New vs Refill Pattern - Time Chart -->
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                            <div style="font-weight: 600; color: #2d3748;" title="Monthly breakdown of new prescriptions versus refills showing treatment persistence patterns over time">📋 New vs Refill Patterns</div>
                                            <select style="border: 1px solid #e2e8f0; border-radius: 4px; padding: 4px 8px; font-size: 12px;">
                                                <option>Monthly</option>
                                                <option selected>Quarterly</option>
                                                <option>Yearly</option>
                                            </select>
                                        </div>
                                        <!-- Simulated Stacked Area Chart -->
                                        <div style="position: relative; height: 120px; border-radius: 6px; margin-bottom: 10px; overflow: hidden;">
                                            <svg width="100%" height="100%" style="position: absolute; top: 0; left: 0;">
                                                <!-- Refills (bottom layer) -->
                                                <polygon points="10,120 30,120 50,120 70,120 90,120 110,120 130,120 150,120 170,120 190,120 210,120 230,120 230,45 210,42 190,40 170,38 150,35 130,33 110,30 90,28 70,25 50,23 30,20 10,18" 
                                                    fill="#38a169" opacity="0.8"/>
                                                <!-- New prescriptions (top layer) -->
                                                <polygon points="10,18 30,20 50,23 70,25 90,28 110,30 130,33 150,35 170,38 190,40 210,42 230,45 230,25 210,22 190,20 170,18 150,15 130,13 110,10 90,8 70,5 50,3 30,0 10,0" 
                                                    fill="#3182ce" opacity="0.8"/>
                                            </svg>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; font-size: 11px; color: #94a3b8; margin-bottom: 10px;">
                                            <span>Q1 '23</span>
                                            <span>Q4 '24</span>
                                        </div>
                                        <div style="display: flex; justify-content: center; gap: 15px; margin-top: 10px;">
                                            <div style="display: flex; align-items: center; gap: 5px;">
                                                <div style="width: 12px; height: 12px; background: #3182ce; border-radius: 2px;"></div>
                                                <span style="font-size: 12px; color: #64748b;">New (37%)</span>
                                            </div>
                                            <div style="display: flex; align-items: center; gap: 5px;">
                                                <div style="width: 12px; height: 12px; background: #38a169; border-radius: 2px;"></div>
                                                <span style="font-size: 12px; color: #64748b;">Refills (63%)</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Average Prescriber Volume - Time Chart -->
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px; grid-column: 1 / -1;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                            <div style="font-weight: 600; color: #2d3748;" title="Average monthly prescription volume per prescribing NPI showing individual provider productivity trends">👨‍⚕️ Average Prescriber Volume Trends</div>
                                            <select style="border: 1px solid #e2e8f0; border-radius: 4px; padding: 4px 8px; font-size: 12px;">
                                                <option selected>Monthly</option>
                                                <option>Quarterly</option>
                                                <option>By Specialty</option>
                                            </select>
                                        </div>
                                        <!-- Simulated Line Chart with Multiple Lines -->
                                        <div style="position: relative; height: 140px; background: linear-gradient(to top, #fef9e7 0%, transparent 100%); border-radius: 6px; margin-bottom: 15px;">
                                            <svg width="100%" height="100%" style="position: absolute; top: 0; left: 0;">
                                                <!-- Grid lines -->
                                                <line x1="0" y1="35" x2="100%" y2="35" stroke="#f1f5f9" stroke-width="1"/>
                                                <line x1="0" y1="70" x2="100%" y2="70" stroke="#f1f5f9" stroke-width="1"/>
                                                <line x1="0" y1="105" x2="100%" y2="105" stroke="#f1f5f9" stroke-width="1"/>
                                                
                                                <!-- Average line -->
                                                <polyline points="20,100 40,95 60,90 80,85 100,80 120,78 140,75 160,73 180,70 200,68 220,65 240,63" 
                                                    fill="none" stroke="#d69e2e" stroke-width="3" stroke-linecap="round"/>
                                                <!-- Top performers line -->
                                                <polyline points="20,60 40,58 60,55 80,52 100,48 120,45 140,42 160,40 180,38 200,35 220,33 240,30" 
                                                    fill="none" stroke="#38a169" stroke-width="2" stroke-linecap="round" stroke-dasharray="5,5"/>
                                                <circle cx="240" cy="63" r="4" fill="#d69e2e"/>
                                                <circle cx="240" cy="30" r="3" fill="#38a169"/>
                                            </svg>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; font-size: 11px; color: #94a3b8; margin-bottom: 15px;">
                                            <span>Jan '23</span>
                                            <span>Dec '24</span>
                                        </div>
                                        <div style="display: flex; justify-content: center; gap: 20px;">
                                            <div style="display: flex; align-items: center; gap: 8px;">
                                                <div style="width: 16px; height: 3px; background: #d69e2e; border-radius: 2px;"></div>
                                                <span style="font-size: 13px; color: #64748b;">Average: 97 Rx/NPI</span>
                                            </div>
                                            <div style="display: flex; align-items: center; gap: 8px;">
                                                <div style="width: 16px; height: 2px; background: #38a169; border-radius: 2px; border: 2px dashed #38a169;"></div>
                                                <span style="font-size: 13px; color: #64748b;">Top 10%: 245 Rx/NPI</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Treatment Patterns Section -->
                        <div class="content-section" id="treatment-patterns-section">
                            <div style="margin-bottom: 25px;">
                                <h2 style="color: #333; font-size: 28px; font-weight: 700; margin: 0;">Treatment Patterns</h2>
                            </div>
                            
                            <!-- Treatment Summary -->
                            <div style="background: rgba(49, 130, 206, 0.1); border-radius: 8px; padding: 20px; margin-bottom: 25px; border-left: 4px solid #3182ce;">
                                <h3 style="color: #2d3748; font-size: 16px; font-weight: 600; margin-bottom: 10px;">💊 Claims-Based Treatment Analysis</h3>
                                <p style="margin: 0; color: #4a5568; font-size: 14px; line-height: 1.6;">
                                    Analysis of treatment patterns derived from prescription chronology in claims data, including line of therapy progression, persistence, and switching behaviors.
                                </p>
                            </div>

                            <!-- Line of Therapy Analysis - Fancy BANs -->
                            <div style="margin-bottom: 30px;">
                                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px;">
                                    <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin: 0; display: flex; align-items: center; gap: 8px;">
                                        🎯 Line of Therapy Analysis (Claims-Sequence Based)
                                    </h3>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <label style="font-size: 13px; color: #64748b; font-weight: 500;">Time Period:</label>
                                        <select style="border: 1px solid #e2e8f0; border-radius: 6px; padding: 6px 12px; font-size: 13px; background: white;">
                                            <option value="12m">Last 12 Months</option>
                                            <option value="24m" selected>Last 24 Months</option>
                                            <option value="36m">Last 36 Months</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px;">
                                    <!-- 1L Drug Utilization -->
                                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 16px; padding: 25px; color: white; position: relative; overflow: hidden;">
                                        <div style="position: absolute; top: -10px; right: -10px; width: 60px; height: 60px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.3;"></div>
                                        <div style="position: relative; z-index: 2;">
                                            <div style="font-size: 12px; font-weight: 600; margin-bottom: 8px; opacity: 0.9;">🥇 1L UTILIZATION</div>
                                            <div style="font-size: 36px; font-weight: 700; margin-bottom: 8px;">67%</div>
                                            <div style="font-size: 13px; opacity: 0.85;">First-line therapy patients</div>
                                        </div>
                                    </div>
                                    
                                    <!-- 2L Drug Utilization -->
                                    <div style="background: linear-gradient(135deg, #38a169 0%, #48bb78 100%); border-radius: 16px; padding: 25px; color: white; position: relative; overflow: hidden;">
                                        <div style="position: absolute; top: -10px; right: -10px; width: 60px; height: 60px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.3;"></div>
                                        <div style="position: relative; z-index: 2;">
                                            <div style="font-size: 12px; font-weight: 600; margin-bottom: 8px; opacity: 0.9;">🥈 2L UTILIZATION</div>
                                            <div style="font-size: 36px; font-weight: 700; margin-bottom: 8px;">28%</div>
                                            <div style="font-size: 13px; opacity: 0.85;">Second-line therapy patients</div>
                                        </div>
                                    </div>
                                    
                                    <!-- 3L+ Drug Utilization -->
                                    <div style="background: linear-gradient(135deg, #d69e2e 0%, #f6ad55 100%); border-radius: 16px; padding: 25px; color: white; position: relative; overflow: hidden;">
                                        <div style="position: absolute; top: -10px; right: -10px; width: 60px; height: 60px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.3;"></div>
                                        <div style="position: relative; z-index: 2;">
                                            <div style="font-size: 12px; font-weight: 600; margin-bottom: 8px; opacity: 0.9;">🥉 3L+ UTILIZATION</div>
                                            <div style="font-size: 36px; font-weight: 700; margin-bottom: 8px;">5%</div>
                                            <div style="font-size: 13px; opacity: 0.85;">Third-line+ therapy patients</div>
                                        </div>
                                    </div>
                                    
                                    <!-- Average Time-to-Next-Treatment -->
                                    <div style="background: linear-gradient(135deg, #805ad5 0%, #b794f6 100%); border-radius: 16px; padding: 25px; color: white; position: relative; overflow: hidden;">
                                        <div style="position: absolute; top: -10px; right: -10px; width: 60px; height: 60px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.3;"></div>
                                        <div style="position: relative; z-index: 2;">
                                            <div style="font-size: 12px; font-weight: 600; margin-bottom: 8px; opacity: 0.9;">⏱️ TIME TO NEXT</div>
                                            <div style="font-size: 36px; font-weight: 700; margin-bottom: 8px;">14.7</div>
                                            <div style="font-size: 13px; opacity: 0.85;">Months average progression</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Treatment Persistence Analysis - Charts -->
                            <div style="margin-bottom: 30px;">
                                <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin-bottom: 20px; display: flex; align-items: center; gap: 8px;">
                                    📈 Treatment Persistence Analysis
                                </h3>
                                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
                                    
                                    <!-- Monthly Persistence Chart -->
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                            <div style="font-weight: 600; color: #2d3748;" title="Monthly persistence rates showing how many patients remain on therapy over time">📊 Monthly Persistence</div>
                                            <select style="border: 1px solid #e2e8f0; border-radius: 4px; padding: 4px 8px; font-size: 12px;">
                                                <option>All Therapies</option>
                                                <option>1L Therapy</option>
                                                <option>2L+ Therapy</option>
                                            </select>
                                        </div>
                                        <!-- Simulated Step Chart -->
                                        <div style="position: relative; height: 120px; background: linear-gradient(to top, #f0f8ff 0%, transparent 100%); border-radius: 6px; margin-bottom: 10px;">
                                            <svg width="100%" height="100%" style="position: absolute; top: 0; left: 0;">
                                                <!-- Step chart showing persistence decline -->
                                                <polyline points="10,20 30,25 50,35 70,45 90,55 110,65 130,75 150,80 170,85 190,90 210,95 230,100" 
                                                    fill="none" stroke="#3182ce" stroke-width="3" stroke-linecap="round"/>
                                                <circle cx="70" cy="45" r="3" fill="#3182ce"/> <!-- 6 month marker -->
                                                <circle cx="130" cy="75" r="3" fill="#3182ce"/> <!-- 12 month marker -->
                                            </svg>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; font-size: 11px; color: #94a3b8; margin-bottom: 10px;">
                                            <span>Month 1</span>
                                            <span>Month 24</span>
                                        </div>
                                        <div style="text-align: center;">
                                            <div style="font-size: 18px; font-weight: 700; color: #3182ce; margin-bottom: 5px;">73.4%</div>
                                            <div style="font-size: 13px; color: #718096;">6-month persistence rate</div>
                                        </div>
                                    </div>

                                    <!-- Average Treatment Duration Chart -->
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                            <div style="font-weight: 600; color: #2d3748;" title="Distribution of treatment durations showing typical patterns before switching or discontinuation">🔄 Average Treatment Duration</div>
                                            <select style="border: 1px solid #e2e8f0; border-radius: 4px; padding: 4px 8px; font-size: 12px;">
                                                <option>By Therapy Line</option>
                                                <option>By Drug Class</option>
                                                <option selected>Overall</option>
                                            </select>
                                        </div>
                                        <!-- Simulated Histogram -->
                                        <div style="display: flex; align-items: end; height: 120px; gap: 4px; margin-bottom: 10px; justify-content: center;">
                                            <div style="background: linear-gradient(to top, #38a169, #68d391); width: 15px; height: 30%; border-radius: 2px 2px 0 0;"></div>
                                            <div style="background: linear-gradient(to top, #38a169, #68d391); width: 15px; height: 45%; border-radius: 2px 2px 0 0;"></div>
                                            <div style="background: linear-gradient(to top, #38a169, #68d391); width: 15px; height: 70%; border-radius: 2px 2px 0 0;"></div>
                                            <div style="background: linear-gradient(to top, #38a169, #68d391); width: 15px; height: 100%; border-radius: 2px 2px 0 0;"></div>
                                            <div style="background: linear-gradient(to top, #38a169, #68d391); width: 15px; height: 85%; border-radius: 2px 2px 0 0;"></div>
                                            <div style="background: linear-gradient(to top, #38a169, #68d391); width: 15px; height: 60%; border-radius: 2px 2px 0 0;"></div>
                                            <div style="background: linear-gradient(to top, #38a169, #68d391); width: 15px; height: 40%; border-radius: 2px 2px 0 0;"></div>
                                            <div style="background: linear-gradient(to top, #38a169, #68d391); width: 15px; height: 25%; border-radius: 2px 2px 0 0;"></div>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; font-size: 11px; color: #94a3b8; margin-bottom: 10px;">
                                            <span>3mo</span>
                                            <span>24mo</span>
                                        </div>
                                        <div style="text-align: center;">
                                            <div style="font-size: 18px; font-weight: 700; color: #38a169; margin-bottom: 5px;">11.3</div>
                                            <div style="font-size: 13px; color: #718096;">Average months on therapy</div>
                                        </div>
                                    </div>

                                    <!-- Discontinuation Rate Chart -->
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                            <div style="font-weight: 600; color: #2d3748;" title="Cumulative discontinuation rates over time showing when patients stop therapy">🚫 Discontinuation Rate</div>
                                            <select style="border: 1px solid #e2e8f0; border-radius: 4px; padding: 4px 8px; font-size: 12px;">
                                                <option>6 Months</option>
                                                <option selected>12 Months</option>
                                                <option>24 Months</option>
                                            </select>
                                        </div>
                                        <!-- Simulated Donut Chart -->
                                        <div style="display: flex; justify-content: center; margin-bottom: 15px;">
                                            <div style="position: relative; width: 100px; height: 100px;">
                                                <svg width="100" height="100" style="transform: rotate(-90deg);">
                                                    <circle cx="50" cy="50" r="35" fill="none" stroke="#f1f5f9" stroke-width="12"/>
                                                    <circle cx="50" cy="50" r="35" fill="none" stroke="#e53e3e" stroke-width="12" 
                                                        stroke-dasharray="68.62" stroke-dashoffset="151.32" stroke-linecap="round"/>
                                                </svg>
                                                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                                                    <div style="font-size: 18px; font-weight: 700; color: #e53e3e;">31.2%</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div style="text-align: center;">
                                            <div style="font-size: 13px; color: #718096;">12-month discontinuation rate</div>
                                        </div>
                                        <div style="display: flex; justify-content: center; gap: 10px; margin-top: 10px;">
                                            <div style="display: flex; align-items: center; gap: 5px;">
                                                <div style="width: 10px; height: 10px; background: #e53e3e; border-radius: 50%;"></div>
                                                <span style="font-size: 11px; color: #64748b;">Discontinued</span>
                                            </div>
                                            <div style="display: flex; align-items: center; gap: 5px;">
                                                <div style="width: 10px; height: 10px; background: #f1f5f9; border-radius: 50%;"></div>
                                                <span style="font-size: 11px; color: #64748b;">Continuing</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>



                            <!-- Market Share Analysis - Bar Chart -->
                            <div style="margin-bottom: 25px;">
                                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px;">
                                    <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin: 0; display: flex; align-items: center; gap: 8px;">
                                        📊 Market Share by Therapy Line (NDC-Based)
                                    </h3>
                                    <div style="display: flex; align-items: center; gap: 15px;">
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <label style="font-size: 13px; color: #64748b; font-weight: 500;">Therapy Line:</label>
                                            <select style="border: 1px solid #e2e8f0; border-radius: 6px; padding: 6px 12px; font-size: 13px; background: white;">
                                                <option value="1l" selected>1L Therapy</option>
                                                <option value="2l">2L Therapy</option>
                                                <option value="3l">3L+ Therapy</option>
                                            </select>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <label style="font-size: 13px; color: #64748b; font-weight: 500;">Time Period:</label>
                                            <select style="border: 1px solid #e2e8f0; border-radius: 6px; padding: 6px 12px; font-size: 13px; background: white;">
                                                <option value="12m">Last 12 Months</option>
                                                <option value="24m" selected>Last 24 Months</option>
                                                <option value="36m">Last 36 Months</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 25px;">
                                    <!-- Market Share Bar Chart for 1L Therapy -->
                                    <div style="margin-bottom: 20px;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 15px; font-size: 16px;">🥇 First-Line Therapy Market Share</div>
                                        <div style="display: grid; gap: 15px;">
                                            
                                            <div style="display: flex; align-items: center; gap: 15px;">
                                                <div style="min-width: 150px; font-weight: 500; color: #2d3748; font-size: 14px;">Carboplatin/Gemcitabine</div>
                                                <div style="flex: 1; position: relative; height: 30px; background: #f1f5f9; border-radius: 15px; overflow: hidden;">
                                                    <div style="height: 100%; width: 42%; background: linear-gradient(to right, #3182ce, #63b3ed); border-radius: 15px; display: flex; align-items: center; justify-content: end; padding-right: 12px;">
                                                        <span style="color: white; font-weight: 600; font-size: 12px;">42%</span>
                                                    </div>
                                                </div>
                                                <div style="min-width: 80px; text-align: right; font-size: 12px; color: #64748b;">168,432 Rx</div>
                                            </div>
                                            
                                            <div style="display: flex; align-items: center; gap: 15px;">
                                                <div style="min-width: 150px; font-weight: 500; color: #2d3748; font-size: 14px;">Cisplatin/Gemcitabine</div>
                                                <div style="flex: 1; position: relative; height: 30px; background: #f1f5f9; border-radius: 15px; overflow: hidden;">
                                                    <div style="height: 100%; width: 31%; background: linear-gradient(to right, #38a169, #68d391); border-radius: 15px; display: flex; align-items: center; justify-content: end; padding-right: 12px;">
                                                        <span style="color: white; font-weight: 600; font-size: 12px;">31%</span>
                                                    </div>
                                                </div>
                                                <div style="min-width: 80px; text-align: right; font-size: 12px; color: #64748b;">124,387 Rx</div>
                                            </div>
                                            
                                            <div style="display: flex; align-items: center; gap: 15px;">
                                                <div style="min-width: 150px; font-weight: 500; color: #2d3748; font-size: 14px;">Other Combinations</div>
                                                <div style="flex: 1; position: relative; height: 30px; background: #f1f5f9; border-radius: 15px; overflow: hidden;">
                                                    <div style="height: 100%; width: 27%; background: linear-gradient(to right, #d69e2e, #f6ad55); border-radius: 15px; display: flex; align-items: center; justify-content: end; padding-right: 12px;">
                                                        <span style="color: white; font-weight: 600; font-size: 12px;">27%</span>
                                                    </div>
                                                </div>
                                                <div style="min-width: 80px; text-align: right; font-size: 12px; color: #64748b;">108,213 Rx</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Market Insights -->
                                    <div style="background: #f8fafc; border-radius: 8px; padding: 15px; border-left: 4px solid #3182ce;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 8px; font-size: 14px;">💡 Market Insights</div>
                                        <div style="font-size: 13px; color: #4a5568; line-height: 1.5;">
                                            <strong>Carboplatin/Gemcitabine</strong> maintains market leadership in 1L therapy with 42% share. 
                                            <strong>Cisplatin-based regimens</strong> show steady utilization at 31%, while emerging combinations capture 27% market share, 
                                            indicating treatment diversification trends.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Patient Complexity Section -->
                        <div class="content-section" id="patient-complexity-section">
                            <div style="margin-bottom: 25px;">
                                <h2 style="color: #333; font-size: 28px; font-weight: 700; margin: 0;">Patient Complexity</h2>
                            </div>
                            
                            <!-- Summary -->
                            <div style="background: rgba(214, 158, 46, 0.1); border-radius: 8px; padding: 20px; margin-bottom: 25px; border-left: 4px solid #d69e2e;">
                                <h3 style="color: #2d3748; font-size: 16px; font-weight: 600; margin-bottom: 10px;">🧬 Patient Complexity Analysis</h3>
                                <p style="margin: 0; color: #4a5568; font-size: 14px; line-height: 1.6;">
                                    Analysis of patient complexity based on concurrent ICD codes, CPT procedure patterns, and healthcare utilization from claims data to understand treatment burden and care coordination needs.
                                </p>
                            </div>

                            <!-- Most Frequent Secondary Diagnoses - Bar Chart -->
                            <div style="margin-bottom: 30px;">
                                <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin-bottom: 20px; display: flex; align-items: center; gap: 8px;">
                                    🔍 Most Frequent Secondary Diagnoses
                                </h3>
                                
                                <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 25px;">
                                    <!-- Horizontal Bar Chart for Secondary Diagnoses -->
                                    <div style="display: grid; gap: 20px;">
                                        <div style="display: flex; align-items: center; gap: 15px;">
                                            <div style="min-width: 140px; font-weight: 600; color: #2d3748; font-size: 14px;">🫁 Chronic Kidney Disease</div>
                                            <div style="flex: 1; position: relative; height: 35px; background: #f1f5f9; border-radius: 18px; overflow: hidden;">
                                                <div style="height: 100%; width: 47%; background: linear-gradient(to right, #3182ce, #63b3ed); border-radius: 18px; display: flex; align-items: center; justify-content: end; padding-right: 15px;">
                                                    <span style="color: white; font-weight: 600; font-size: 13px;">47%</span>
                                                </div>
                                            </div>
                                            <div style="min-width: 90px; text-align: right; font-size: 12px; color: #64748b;">ICD: N18.x</div>
                                        </div>
                                        
                                        <div style="display: flex; align-items: center; gap: 15px;">
                                            <div style="min-width: 140px; font-weight: 600; color: #2d3748; font-size: 14px;">🫀 Hypertension</div>
                                            <div style="flex: 1; position: relative; height: 35px; background: #f1f5f9; border-radius: 18px; overflow: hidden;">
                                                <div style="height: 100%; width: 41%; background: linear-gradient(to right, #38a169, #68d391); border-radius: 18px; display: flex; align-items: center; justify-content: end; padding-right: 15px;">
                                                    <span style="color: white; font-weight: 600; font-size: 13px;">41%</span>
                                                </div>
                                            </div>
                                            <div style="min-width: 90px; text-align: right; font-size: 12px; color: #64748b;">ICD: I10-I15</div>
                                        </div>
                                        
                                        <div style="display: flex; align-items: center; gap: 15px;">
                                            <div style="min-width: 140px; font-weight: 600; color: #2d3748; font-size: 14px;">🍯 Diabetes Mellitus</div>
                                            <div style="flex: 1; position: relative; height: 35px; background: #f1f5f9; border-radius: 18px; overflow: hidden;">
                                                <div style="height: 100%; width: 38%; background: linear-gradient(to right, #d69e2e, #f6ad55); border-radius: 18px; display: flex; align-items: center; justify-content: end; padding-right: 15px;">
                                                    <span style="color: white; font-weight: 600; font-size: 13px;">38%</span>
                                                </div>
                                            </div>
                                            <div style="min-width: 90px; text-align: right; font-size: 12px; color: #64748b;">ICD: E08-E13</div>
                                        </div>
                                        
                                        <div style="display: flex; align-items: center; gap: 15px;">
                                            <div style="min-width: 140px; font-weight: 600; color: #2d3748; font-size: 14px;">😰 Anxiety/Depression</div>
                                            <div style="flex: 1; position: relative; height: 35px; background: #f1f5f9; border-radius: 18px; overflow: hidden;">
                                                <div style="height: 100%; width: 32%; background: linear-gradient(to right, #805ad5, #b794f6); border-radius: 18px; display: flex; align-items: center; justify-content: end; padding-right: 15px;">
                                                    <span style="color: white; font-weight: 600; font-size: 13px;">32%</span>
                                                </div>
                                            </div>
                                            <div style="min-width: 90px; text-align: right; font-size: 12px; color: #64748b;">ICD: F32-F43</div>
                                        </div>
                                        
                                        <div style="display: flex; align-items: center; gap: 15px;">
                                            <div style="min-width: 140px; font-weight: 600; color: #2d3748; font-size: 14px;">🫁 COPD/Respiratory</div>
                                            <div style="flex: 1; position: relative; height: 35px; background: #f1f5f9; border-radius: 18px; overflow: hidden;">
                                                <div style="height: 100%; width: 28%; background: linear-gradient(to right, #e53e3e, #fc8181); border-radius: 18px; display: flex; align-items: center; justify-content: end; padding-right: 15px;">
                                                    <span style="color: white; font-weight: 600; font-size: 13px;">28%</span>
                                                </div>
                                            </div>
                                            <div style="min-width: 90px; text-align: right; font-size: 12px; color: #64748b;">ICD: J40-J47</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Most Common Diagnostic Procedures - Bar Chart -->
                            <div style="margin-bottom: 30px;">
                                <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin-bottom: 20px; display: flex; align-items: center; gap: 8px;">
                                    🔬 Most Common Diagnostic Procedures
                                </h3>
                                
                                <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 25px;">
                                    <!-- Horizontal Bar Chart for Diagnostic Procedures -->
                                    <div style="display: grid; gap: 20px;">
                                        <div style="display: flex; align-items: center; gap: 15px;">
                                            <div style="min-width: 160px; font-weight: 600; color: #2d3748; font-size: 14px;">🩻 CT Scan (Abdomen/Pelvis)</div>
                                            <div style="flex: 1; position: relative; height: 35px; background: #f1f5f9; border-radius: 18px; overflow: hidden;">
                                                <div style="height: 100%; width: 73%; background: linear-gradient(to right, #3182ce, #63b3ed); border-radius: 18px; display: flex; align-items: center; justify-content: end; padding-right: 15px;">
                                                    <span style="color: white; font-weight: 600; font-size: 13px;">73%</span>
                                                </div>
                                            </div>
                                            <div style="min-width: 90px; text-align: right; font-size: 12px; color: #64748b;">CPT: 74177</div>
                                        </div>
                                        
                                        <div style="display: flex; align-items: center; gap: 15px;">
                                            <div style="min-width: 160px; font-weight: 600; color: #2d3748; font-size: 14px;">🔬 Urinalysis Complete</div>
                                            <div style="flex: 1; position: relative; height: 35px; background: #f1f5f9; border-radius: 18px; overflow: hidden;">
                                                <div style="height: 100%; width: 68%; background: linear-gradient(to right, #38a169, #68d391); border-radius: 18px; display: flex; align-items: center; justify-content: end; padding-right: 15px;">
                                                    <span style="color: white; font-weight: 600; font-size: 13px;">68%</span>
                                                </div>
                                            </div>
                                            <div style="min-width: 90px; text-align: right; font-size: 12px; color: #64748b;">CPT: 81001</div>
                                        </div>
                                        
                                        <div style="display: flex; align-items: center; gap: 15px;">
                                            <div style="min-width: 160px; font-weight: 600; color: #2d3748; font-size: 14px;">🧪 Comprehensive Metabolic Panel</div>
                                            <div style="flex: 1; position: relative; height: 35px; background: #f1f5f9; border-radius: 18px; overflow: hidden;">
                                                <div style="height: 100%; width: 61%; background: linear-gradient(to right, #d69e2e, #f6ad55); border-radius: 18px; display: flex; align-items: center; justify-content: end; padding-right: 15px;">
                                                    <span style="color: white; font-weight: 600; font-size: 13px;">61%</span>
                                                </div>
                                            </div>
                                            <div style="min-width: 90px; text-align: right; font-size: 12px; color: #64748b;">CPT: 80053</div>
                                        </div>
                                        
                                        <div style="display: flex; align-items: center; gap: 15px;">
                                            <div style="min-width: 160px; font-weight: 600; color: #2d3748; font-size: 14px;">🔍 Cystoscopy Diagnostic</div>
                                            <div style="flex: 1; position: relative; height: 35px; background: #f1f5f9; border-radius: 18px; overflow: hidden;">
                                                <div style="height: 100%; width: 54%; background: linear-gradient(to right, #805ad5, #b794f6); border-radius: 18px; display: flex; align-items: center; justify-content: end; padding-right: 15px;">
                                                    <span style="color: white; font-weight: 600; font-size: 13px;">54%</span>
                                                </div>
                                            </div>
                                            <div style="min-width: 90px; text-align: right; font-size: 12px; color: #64748b;">CPT: 52000</div>
                                        </div>
                                        
                                        <div style="display: flex; align-items: center; gap: 15px;">
                                            <div style="min-width: 160px; font-weight: 600; color: #2d3748; font-size: 14px;">🩺 MRI Pelvis w/ Contrast</div>
                                            <div style="flex: 1; position: relative; height: 35px; background: #f1f5f9; border-radius: 18px; overflow: hidden;">
                                                <div style="height: 100%; width: 47%; background: linear-gradient(to right, #e53e3e, #fc8181); border-radius: 18px; display: flex; align-items: center; justify-content: end; padding-right: 15px;">
                                                    <span style="color: white; font-weight: 600; font-size: 13px;">47%</span>
                                                </div>
                                            </div>
                                            <div style="min-width: 90px; text-align: right; font-size: 12px; color: #64748b;">CPT: 72197</div>
                                        </div>
                                        
                                        <div style="display: flex; align-items: center; gap: 15px;">
                                            <div style="min-width: 160px; font-weight: 600; color: #2d3748; font-size: 14px;">🧬 Tissue Biopsy</div>
                                            <div style="flex: 1; position: relative; height: 35px; background: #f1f5f9; border-radius: 18px; overflow: hidden;">
                                                <div style="height: 100%; width: 42%; background: linear-gradient(to right, #0891b2, #06b6d4); border-radius: 18px; display: flex; align-items: center; justify-content: end; padding-right: 15px;">
                                                    <span style="color: white; font-weight: 600; font-size: 13px;">42%</span>
                                                </div>
                                            </div>
                                            <div style="min-width: 90px; text-align: right; font-size: 12px; color: #64748b;">CPT: 88305</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Claims-Based Insights - BANs -->
                            <div style="margin-bottom: 25px;">
                                <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin-bottom: 20px; display: flex; align-items: center; gap: 8px;">
                                    💡 Claims-Based Complexity Insights
                                </h3>
                                
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                    <!-- Total Cost of Care BAN -->
                                    <div style="background: linear-gradient(135deg, #e53e3e 0%, #fc8181 100%); border-radius: 16px; padding: 25px; color: white; position: relative; overflow: hidden;">
                                        <div style="position: absolute; top: -10px; right: -10px; width: 60px; height: 60px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.3;"></div>
                                        <div style="position: relative; z-index: 2;">
                                            <div style="font-size: 12px; font-weight: 600; margin-bottom: 8px; opacity: 0.9;">💰 TOTAL COST OF CARE</div>
                                            <div style="font-size: 36px; font-weight: 700; margin-bottom: 8px;">$216K</div>
                                            <div style="font-size: 13px; opacity: 0.85;">Annual per patient (Medical + Pharmacy)</div>
                                            <div style="margin-top: 10px; font-size: 12px; opacity: 0.8;">
                                                Medical: $127K • Pharmacy: $89K
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Healthcare Touches BAN -->
                                    <div style="background: linear-gradient(135deg, #38a169 0%, #48bb78 100%); border-radius: 16px; padding: 25px; color: white; position: relative; overflow: hidden;">
                                        <div style="position: absolute; top: -10px; right: -10px; width: 60px; height: 60px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.3;"></div>
                                        <div style="position: relative; z-index: 2;">
                                            <div style="font-size: 12px; font-weight: 600; margin-bottom: 8px; opacity: 0.9;">🏥 HEALTHCARE TOUCHES</div>
                                            <div style="font-size: 36px; font-weight: 700; margin-bottom: 8px;">23</div>
                                            <div style="font-size: 13px; opacity: 0.85;">Days from diagnosis to treatment</div>
                                            <div style="margin-top: 10px; font-size: 12px; opacity: 0.8;">
                                                4.2 specialists • 2.3 ER visits/year
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>

    <!-- KPI Modal -->
    <div id="kpiModal" class="kpi-modal">
        <div class="kpi-modal-content">
            <div class="kpi-modal-header">
                <h2 class="kpi-modal-title" id="kpiModalTitle">KPI Analysis</h2>
                <button class="kpi-modal-close" onclick="closeKPIModal()">&times;</button>
                    </div>
            <div class="kpi-modal-body" id="kpiModalBody">
                <!-- KPI content will be populated dynamically -->
                    </div>
        </div>
    </div>



    <!-- Chatbot Modal -->
    <div id="chatbotModal" class="chatbot-modal">
        <div class="chatbot-modal-content">
            <div class="chatbot-header">
                <h2 class="chatbot-title">
                    🤖 Claims Analytics Intelligence
                </h2>
                <button class="chatbot-close" onclick="closeChatbot()">&times;</button>
            </div>
            <div class="chatbot-body">
                <!-- Suggested Questions -->
                <div class="suggested-questions">
                    <h4>💡 Suggested Questions:</h4>
                    <span class="suggestion-chip" onclick="askSuggestion('What\'s the average time between diagnosis and first treatment?')">Average time to treatment</span>
                    <span class="suggestion-chip" onclick="askSuggestion('Which specialists prescribe the most second-line therapies?')">2L specialist prescribers</span>
                    <span class="suggestion-chip" onclick="askSuggestion('Show me the top 5 drug switching patterns')">Top switching patterns</span>
                    <span class="suggestion-chip" onclick="askSuggestion('Which geographic regions have highest per-capita utilization?')">Geographic utilization</span>
                </div>

                <!-- Chat Messages -->
                <div class="chat-messages" id="chatMessages">
                    <!-- Welcome message -->
                    <div class="message bot">
                        <div class="message-avatar">🤖</div>
                        <div>
                            <div class="message-content">
                                Hello! I'm your Claims Analytics Intelligence assistant. I can help you analyze claims data patterns, treatment timelines, prescriber behaviors, and geographic utilization trends. What would you like to know?
                            </div>
                            <div class="message-time">Just now</div>
                        </div>
                    </div>
                </div>

                <!-- Typing Indicator -->
                <div class="typing-indicator" id="typingIndicator">
                    <div class="message bot">
                        <div class="message-avatar">🤖</div>
                        <div class="typing-dots">
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                        </div>
                    </div>
                </div>

                <!-- Chat Input -->
                <div class="chat-input-area">
                    <div class="chat-input-container">
                        <input type="text" class="chat-input" id="chatInput" placeholder="Ask me anything about claims analytics..." onkeypress="handleChatKeyPress(event)">
                        <button class="chat-send-btn" onclick="sendMessage()">➤</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedTherapy = null;
        let selectedIndication = null;
        let selectedIndicationName = null;
        let selectedCategory = null;
        let currentTherapyPage = 1;
        let currentIndicationPage = 1;
        let totalIndicationPages = 1;

        function selectTherapy(therapy) {
            selectedTherapy = therapy;
            
            // Update therapy card selection
            document.querySelectorAll('.therapy-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-therapy="${therapy}"]`).classList.add('selected');
            
            // Update progress indicators
            document.getElementById('therapyCircle').classList.add('active');
            
            // Show indication section with animation
            const indicationSection = document.getElementById('indicationSection');
            indicationSection.style.display = 'block';
            setTimeout(() => {
                indicationSection.classList.add('show');
            }, 100);
            
            // Populate indications based on therapy
            populateIndications(therapy);
            
            // Clear indication selection
            selectedIndication = null;
            document.getElementById('indicationCircle').classList.remove('active');
            updateContinueButton();
        }

        function changeTherapyPage(direction) {
            const currentPage = document.getElementById(`therapyPage${currentTherapyPage}`);
            const newPageNumber = currentTherapyPage + direction;
            
            if (newPageNumber < 1 || newPageNumber > 2) return;
            
            const newPage = document.getElementById(`therapyPage${newPageNumber}`);
            
            // Animate out current page
            currentPage.classList.remove('active');
            if (direction > 0) {
                currentPage.classList.add('prev');
            } else {
                currentPage.style.transform = 'translateX(100%)';
            }
            
            // Animate in new page
            setTimeout(() => {
                if (direction > 0) {
                    newPage.style.transform = 'translateX(100%)';
                } else {
                    newPage.classList.add('prev');
                }
                
                setTimeout(() => {
                    newPage.classList.add('active');
                    newPage.classList.remove('prev');
                    newPage.style.transform = '';
                }, 50);
            }, 200);
            
            currentTherapyPage = newPageNumber;
            updateTherapyPaginationButtons();
        }

        function updateTherapyPaginationButtons() {
            document.getElementById('therapyPrevBtn').disabled = currentTherapyPage === 1;
            document.getElementById('therapyNextBtn').disabled = currentTherapyPage === 2;
        }

        function changeIndicationPage(direction) {
            const currentPage = document.getElementById(`indicationPage${currentIndicationPage}`);
            const newPageNumber = currentIndicationPage + direction;
            
            if (newPageNumber < 1 || newPageNumber > totalIndicationPages) return;
            
            const newPage = document.getElementById(`indicationPage${newPageNumber}`);
            
            // Animate out current page
            currentPage.classList.remove('active');
            if (direction > 0) {
                currentPage.classList.add('prev');
            } else {
                currentPage.style.transform = 'translateX(100%)';
            }
            
            // Animate in new page
            setTimeout(() => {
                if (direction > 0) {
                    newPage.style.transform = 'translateX(100%)';
                } else {
                    newPage.classList.add('prev');
                }
                
                setTimeout(() => {
                    newPage.classList.add('active');
                    newPage.classList.remove('prev');
                    newPage.style.transform = '';
                }, 50);
            }, 200);
            
            currentIndicationPage = newPageNumber;
            updateIndicationPaginationButtons();
        }

        function updateIndicationPaginationButtons() {
            document.getElementById('indicationPrevBtn').disabled = currentIndicationPage === 1;
            document.getElementById('indicationNextBtn').disabled = currentIndicationPage === totalIndicationPages;
        }

        function populateIndications(therapy) {
            const indicationPagination = document.getElementById('indicationPagination');
            currentIndicationPage = 1; // Reset to first page
            
            const indications = {
                'oncology': [
                    { id: 'urothelial-cancer', name: 'Urothelial Cancer', desc: 'Bladder and urinary tract cancer', icon: '🎗️' },
                    { id: 'lung-cancer', name: 'Lung Cancer', desc: 'Non-small cell lung cancer', icon: '🫁' },
                    { id: 'breast-cancer', name: 'Breast Cancer', desc: 'Hormone receptor positive', icon: '💗' },
                    { id: 'melanoma', name: 'Melanoma', desc: 'Advanced melanoma', icon: '☀️' }
                ],
                'rheumatology': [
                    { id: 'ra', name: 'Rheumatoid Arthritis', desc: 'Autoimmune joint disorder', icon: '🦴' },
                    { id: 'osteoarthritis', name: 'Osteoarthritis', desc: 'Degenerative joint disease', icon: '⚡' },
                    { id: 'lupus', name: 'Lupus', desc: 'Systemic autoimmune disease', icon: '🔥' }
                ],
                'psychiatry': [
                    { id: 'schizophrenia', name: 'Schizophrenia', desc: 'Chronic brain disorder', icon: '🧠' },
                    { id: 'depression', name: 'Depression', desc: 'Major depressive disorder', icon: '😔' },
                    { id: 'bipolar', name: 'Bipolar Disorder', desc: 'Mood disorder', icon: '🔄' }
                ],
                'neurology': [
                    { id: 'epilepsy', name: 'Epilepsy', desc: 'Seizure disorder', icon: '⚡' },
                    { id: 'multiple-sclerosis', name: 'Multiple Sclerosis', desc: 'Chronic CNS disease', icon: '🧬' },
                    { id: 'alzheimers', name: 'Alzheimer\'s Disease', desc: 'Neurodegenerative disorder', icon: '🧠' }
                ],
                'nephrology': [
                    { id: 'ckd', name: 'Chronic Kidney Disease', desc: 'Progressive kidney dysfunction', icon: '🫘' },
                    { id: 'adpkd', name: 'ADPKD', desc: 'Autosomal dominant polycystic kidney disease', icon: '🔗' },
                    { id: 'aki', name: 'Acute Kidney Injury', desc: 'Sudden kidney damage', icon: '🚨' }
                ],
                'others': [
                    { id: 'pku', name: 'PKU', desc: 'Phenylketonuria genetic disorder', icon: '🧬' },
                    { id: 'rare-diseases', name: 'Rare Diseases', desc: 'Orphan drug indications', icon: '💎' }
                ]
            };

            const therapyIndications = indications[therapy] || [];
            const indicationsPerPage = 3;
            totalIndicationPages = Math.ceil(therapyIndications.length / indicationsPerPage);
            
            // Clear existing pages
            indicationPagination.innerHTML = '';
            
            // Create pages
            for (let page = 1; page <= totalIndicationPages; page++) {
                const startIndex = (page - 1) * indicationsPerPage;
                const endIndex = startIndex + indicationsPerPage;
                const pageIndications = therapyIndications.slice(startIndex, endIndex);
                
                const pageDiv = document.createElement('div');
                pageDiv.className = `indication-page ${page === 1 ? 'active' : ''}`;
                pageDiv.id = `indicationPage${page}`;
                
                pageDiv.innerHTML = pageIndications.map(indication => `
                    <div class="indication-card" data-indication="${indication.id}" onclick="selectIndication('${indication.id}', '${indication.name}')">
                        <div class="indication-icon ${indication.id}-icon">${indication.icon}</div>
                        <div class="indication-name">${indication.name}</div>
                        <div class="indication-desc">${indication.desc}</div>
                    </div>
                `).join('');
                
                indicationPagination.appendChild(pageDiv);
            }
            
            updateIndicationPaginationButtons();
        }

        function selectIndication(indicationId, indicationName) {
            selectedIndication = indicationId;
            selectedIndicationName = indicationName;
            
            // Update indication card selection
            document.querySelectorAll('.indication-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-indication="${indicationId}"]`).classList.add('selected');
            
            // Update progress indicators
            document.getElementById('indicationCircle').classList.add('active');
            document.querySelector('.progress-line').classList.add('active');
            
            updateContinueButton();
        }

        function updateContinueButton() {
            const btn = document.getElementById('continueBtn1');
            btn.disabled = !(selectedTherapy && selectedIndication);
        }



        function showStep(stepNumber) {
            document.querySelectorAll('.step').forEach(step => {
                step.classList.remove('active');
            });
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            document.getElementById(`step${stepNumber}`).classList.add('active');
            document.querySelectorAll('.nav-tab')[stepNumber - 1].classList.add('active');
            
            // Update selected indication display when showing step 2
            if (stepNumber === 2 && selectedIndicationName) {
                const indicationDisplay = document.getElementById('selectedIndicationDisplay');
                if (indicationDisplay) {
                    indicationDisplay.textContent = selectedIndicationName;
                }
            }
            
            // Update report content when showing step 3
            if (stepNumber === 3 && selectedTherapy && selectedIndication) {
                updateReportContent();
                enableHeaderButtons();
            } else {
                // Disable buttons if not on step 3 or selections incomplete
                disableHeaderButtons();
            }
        }

        function disableHeaderButtons() {
            const chatBtn = document.getElementById('chatbotBtn');
            
            // Disable Chatbot button
            chatBtn.disabled = true;
            chatBtn.style.opacity = '0.5';
            chatBtn.style.cursor = 'not-allowed';
        }

        function enableHeaderButtons() {
            const chatBtn = document.getElementById('chatbotBtn');
            
            // Enable Chatbot button
            chatBtn.disabled = false;
            chatBtn.style.opacity = '1';
            chatBtn.style.cursor = 'pointer';
        }

        function updateReportContent() {
            const reportTitle = document.getElementById('reportTitle');
            const diseaseSummary = document.getElementById('diseaseSummary');
            const diseaseDescriptionText = document.getElementById('diseaseDescriptionText');
            const diseaseTitle = document.getElementById('diseaseTitle');

            if (reportTitle && diseaseSummary && diseaseDescriptionText && diseaseTitle) {
                // Always show Schizophrenia report regardless of selection
                const indicationName = 'Schizophrenia';
                
                reportTitle.textContent = indicationName;
                diseaseTitle.textContent = indicationName;
                
                // Always use Schizophrenia description
                const schizophreniaDescription = 'Schizophrenia is a chronic and severe mental disorder that affects how a person thinks, feels, and behaves. Characterized by positive symptoms (delusions, hallucinations), negative symptoms (reduced emotional expression, avolition), and cognitive symptoms (impaired working memory, attention deficits), schizophrenia significantly impairs daily functioning across multiple domains. The disorder typically emerges in late adolescence or early adulthood and requires lifelong management. Current treatments focus primarily on positive symptom control through antipsychotic medications, with significant unmet needs remaining in cognitive enhancement, negative symptom management, and functional recovery. The condition affects approximately 1% of the global population and represents one of the leading causes of disability worldwide, imposing substantial burden on patients, families, and healthcare systems.';
                
                diseaseSummary.textContent = schizophreniaDescription;
                diseaseDescriptionText.textContent = schizophreniaDescription;
            }
        }

        function populateKPIDetails(category) {
            const categoryTitles = {
                'disease-overview': 'Disease Overview Analysis',
                'market-evolution': 'Market Evolution Analysis', 
                'patient-insights': 'Patient Insights Analysis',
                'unmet-need': 'Unmet Need Assessment',
                'competitive-intensity': 'Competitive Intensity Analysis'
            };

            document.getElementById('selectedCategoryTitle').textContent = categoryTitles[category];

            const kpiDetails = document.getElementById('kpiDetails');
            
            if (category === 'disease-overview') {
                kpiDetails.innerHTML = `
                    <h3 style="color: #333; margin-bottom: 20px;">Disease Overview - ADHD Analysis</h3>
                    <div class="kpi-grid">
                        <div class="kpi-card">
                            <div class="kpi-title">Disease Clinical Description</div>
                            <div class="kpi-value">Complete</div>
                            <div class="kpi-trend">✅ Comprehensive profile</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Detailed clinical description and pathophysiology available
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Diagnostic Process</div>
                            <div class="kpi-value">DSM-5</div>
                            <div class="kpi-trend">📋 Standardized criteria</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Well-established diagnostic criteria and assessment tools
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Treatment Modalities</div>
                            <div class="kpi-value">Multiple</div>
                            <div class="kpi-trend">💊 Diverse options</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Stimulants, non-stimulants, and behavioral interventions
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Disease Severity Spectrum</div>
                            <div class="kpi-value">Mild-Severe</div>
                            <div class="kpi-trend">📊 Broad range</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Comprehensive severity classification system exists
                            </p>
                        </div>
                    </div>
                    
                    <div style="margin-top: 30px;">
                        <div class="summary-card">
                            <div class="summary-title">Disease Overview Summary</div>
                            <div class="summary-text">
                                ADHD is a well-characterized neurodevelopmental disorder with established DSM-5 diagnostic criteria. The disease presents across a spectrum from mild to severe, affecting attention, hyperactivity, and impulsivity. Multiple treatment modalities exist including stimulant medications, non-stimulant alternatives, and behavioral interventions, providing comprehensive therapeutic options for patients.
                            </div>
                        </div>
                    </div>
                `;
            } else if (category === 'market-evolution') {
                kpiDetails.innerHTML = `
                    <h3 style="color: #333; margin-bottom: 20px;">Market Evolution - ADHD Analysis</h3>
                    <div class="kpi-grid">
                        <div class="kpi-card">
                            <div class="kpi-title">Epidemiology Trends</div>
                            <div class="kpi-value">8.4%</div>
                            <div class="kpi-trend">↗️ Growing prevalence</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Increasing incidence and prevalence rates globally
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Market Size & Growth</div>
                            <div class="kpi-value">$17.9B</div>
                            <div class="kpi-trend">📈 7.3% CAGR</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Global market size with strong growth trajectory
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Current Sales Leaders</div>
                            <div class="kpi-value">Top 3</div>
                            <div class="kpi-trend">💰 $8.2B combined</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Market share across key therapeutic classes
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Future Projections</div>
                            <div class="kpi-value">$24.9B</div>
                            <div class="kpi-trend">🎯 2030 forecast</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Projected market size by 2030 across brands and manufacturers
                            </p>
                        </div>
                    </div>
                    
                    <div style="margin-top: 30px;">
                        <div class="summary-card">
                            <div class="summary-title">Market Evolution Summary</div>
                            <div class="summary-text">
                                The ADHD market shows robust growth with 8.4% prevalence and strong commercial expansion (7.3% CAGR). Current market size of $17.9B is dominated by established stimulant therapies, with projections reaching $24.9B by 2030. Increasing adult diagnosis and recognition of ADHD across demographics drives sustained market evolution and opportunity.
                            </div>
                        </div>
                    </div>
                `;
            } else if (category === 'patient-insights') {
                kpiDetails.innerHTML = `
                    <h3 style="color: #333; margin-bottom: 20px;">Patient Insights - ADHD Analysis</h3>
                    <div class="kpi-grid">
                        <div class="kpi-card">
                            <div class="kpi-title">Patient Demographics</div>
                            <div class="kpi-value">60:40</div>
                            <div class="kpi-trend">👦👧 Male:Female ratio</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Age and gender distribution across patient population
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Key Comorbidities</div>
                            <div class="kpi-value">65%</div>
                            <div class="kpi-trend">🧠 Anxiety/Depression</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Percentage with significant comorbid conditions
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Treatment Preferences</div>
                            <div class="kpi-value">85%</div>
                            <div class="kpi-trend">💊 Oral medications</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Share across treatment modalities by brand and class
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Patient Journey Length</div>
                            <div class="kpi-value">18 mo</div>
                            <div class="kpi-trend">⏱️ Diagnosis to optimal treatment</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Average time from initial symptoms to stable therapy
                            </p>
                        </div>
                    </div>
                    
                    <div style="margin-top: 30px;">
                        <div class="summary-card">
                            <div class="summary-title">Patient Insights Summary</div>
                            <div class="summary-text">
                                ADHD patients show male predominance (60:40) with high comorbidity burden, particularly anxiety and depression (65%). Strong preference for oral medications (85%) reflects convenience needs. The patient journey averages 18 months from diagnosis to optimal treatment, highlighting opportunities for faster therapeutic optimization and improved patient experience.
                            </div>
                        </div>
                    </div>
                `;
            } else if (category === 'unmet-need') {
                kpiDetails.innerHTML = `
                    <h3 style="color: #333; margin-bottom: 20px;">Unmet Need - ADHD Analysis</h3>
                    <div class="kpi-grid">
                        <div class="kpi-card">
                            <div class="kpi-title">Clinical Outcome Gaps</div>
                            <div class="kpi-value">35%</div>
                            <div class="kpi-trend">⚠️ Suboptimal response</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Patients with under/delayed diagnosis, survival, safety concerns
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Patient Experience Challenges</div>
                            <div class="kpi-value">5.4/10</div>
                            <div class="kpi-trend">😞 Below acceptable</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Quality of life impact including pain, AE management, administration
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">KOL Standard of Care Gaps</div>
                            <div class="kpi-value">42%</div>
                            <div class="kpi-trend">📋 Deviation from guidelines</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                KOL opinions and deviation from standard treatment protocols
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Treatment Discontinuation</div>
                            <div class="kpi-value">22%</div>
                            <div class="kpi-trend">⚠️ High discontinuation</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Percentage of patients discontinuing therapy within 12 months
                            </p>
                        </div>
                    </div>
                    
                    <div style="margin-top: 30px;">
                        <div class="summary-card">
                            <div class="summary-title">Unmet Need Summary</div>
                            <div class="summary-text">
                                Significant unmet needs exist with 35% of patients experiencing suboptimal clinical outcomes and poor quality of life scores (5.4/10). KOL surveys indicate 42% deviation from standard care protocols, while high discontinuation rates (22%) reflect tolerability and efficacy challenges. These gaps represent clear opportunities for improved therapeutic approaches and patient management strategies.
                            </div>
                        </div>
                    </div>
                `;
            } else if (category === 'competitive-intensity') {
                kpiDetails.innerHTML = `
                    <h3 style="color: #333; margin-bottom: 20px;">Competitive Intensity - ADHD Analysis</h3>
                    <div class="kpi-grid">
                        <div class="kpi-card">
                            <div class="kpi-title">Market Crowding</div>
                            <div class="kpi-value">Red Ocean</div>
                            <div class="kpi-trend">🌊 Highly competitive</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Blue vs red ocean competitive landscape assessment
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Pipeline Activity</div>
                            <div class="kpi-value">28</div>
                            <div class="kpi-trend">⚠️ High activity</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Number of unique sponsors with Phase II+ assets in disease area and class
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Pricing Pressure</div>
                            <div class="kpi-value">High</div>
                            <div class="kpi-trend">💰 Cost constraints</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Pricing and access hurdles across major markets
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Competitive Risk Score</div>
                            <div class="kpi-value">7.3/10</div>
                            <div class="kpi-trend">🔴 High risk</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Overall risk assessment for market entry and success
                            </p>
                        </div>
                    </div>
                    
                    <div style="margin-top: 30px;">
                        <div class="summary-card">
                            <div class="summary-title">Competitive Intensity Summary</div>
                            <div class="summary-text">
                                ADHD represents a red ocean market with intense competition from 28 active pipeline sponsors. High pricing pressure and access hurdles create additional barriers to entry. The overall competitive risk score of 7.3/10 indicates significant challenges for new entrants, requiring strong differentiation and strategic positioning to achieve commercial success.
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        function toggleInsightCard(card) {
            const expandBtn = card.querySelector('.expand-btn');
            const details = card.querySelector('.insight-details');
            
            if (details.classList.contains('show')) {
                details.classList.remove('show');
                expandBtn.textContent = '📖 Expand';
                expandBtn.classList.remove('expanded');
                card.classList.remove('expanded');
            } else {
                details.classList.add('show');
                expandBtn.textContent = '📚 Collapse';
                expandBtn.classList.add('expanded');
                card.classList.add('expanded');
            }
        }

        function toggleBookmark(event, btn) {
            event.stopPropagation();
            
            if (btn.classList.contains('bookmarked')) {
                btn.classList.remove('bookmarked');
                btn.innerHTML = '⭐ Bookmark';
            } else {
                btn.classList.add('bookmarked');
                btn.innerHTML = '🌟 Bookmarked';
            }
        }

        function openKPIPopup(event, dimension) {
            event.stopPropagation();
            
            const modal = document.getElementById('kpiModal');
            const title = document.getElementById('kpiModalTitle');
            const body = document.getElementById('kpiModalBody');
            
            // Set title and content based on dimension
            const kpiData = getKPIData(dimension);
            title.textContent = kpiData.title;
            body.innerHTML = kpiData.content;
            
            // Show modal
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeKPIModal() {
            const modal = document.getElementById('kpiModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function getKPIData(dimension) {
            const kpiData = {
                'market-evolution': {
                    title: '📈 Market Evolution KPIs',
                    content: `
                        <div class="kpi-description">
                            <p>Comprehensive analysis of schizophrenia market dynamics, growth patterns, and future projections across global markets.</p>
                        </div>
                        
                        <div class="kpi-section">
                            <div class="kpi-section-title">💰 Market Size & Growth</div>
                            <div class="kpi-grid">
                                <div class="kpi-item" style="border-left-color: #38a169;">
                                    <div class="kpi-value">$7.6B</div>
                                    <div class="kpi-label">Current Market Size (2024)</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #3182ce;">
                                    <div class="kpi-value">4.8%</div>
                                    <div class="kpi-label">CAGR (2023-2030)</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #805ad5;">
                                    <div class="kpi-value">$10.7B</div>
                                    <div class="kpi-label">2030 Projected Size</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #d69e2e;">
                                    <div class="kpi-value">41%</div>
                                    <div class="kpi-label">Growth Over 7 Years</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="kpi-section">
                            <div class="kpi-section-title">🌍 Regional Distribution</div>
                            <div class="kpi-grid">
                                <div class="kpi-item" style="border-left-color: #e53e3e;">
                                    <div class="kpi-value">45%</div>
                                    <div class="kpi-label">North America Share</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #38b2ac;">
                                    <div class="kpi-value">32%</div>
                                    <div class="kpi-label">Europe Share</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #dd6b20;">
                                    <div class="kpi-value">18%</div>
                                    <div class="kpi-label">Asia-Pacific Share</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #9f7aea;">
                                    <div class="kpi-value">5%</div>
                                    <div class="kpi-label">Rest of World</div>
                                </div>
                            </div>
                        </div>
                    `
                },
                'patient-profile': {
                    title: '🩺 Patient Profile KPIs',
                    content: `
                        <div class="kpi-description">
                            <p>Detailed demographic and clinical characteristics of schizophrenia patients, including comorbidities and treatment patterns.</p>
                        </div>
                        
                        <div class="kpi-section">
                            <div class="kpi-section-title">👥 Demographics</div>
                            <div class="kpi-grid">
                                <div class="kpi-item" style="border-left-color: #3182ce;">
                                    <div class="kpi-value">1.4:1</div>
                                    <div class="kpi-label">Male to Female Ratio</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #805ad5;">
                                    <div class="kpi-value">22 yrs</div>
                                    <div class="kpi-label">Average Age of Onset</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #38a169;">
                                    <div class="kpi-value">65%</div>
                                    <div class="kpi-label">Diagnosed by Age 25</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #d69e2e;">
                                    <div class="kpi-value">2-5 yrs</div>
                                    <div class="kpi-label">Diagnostic Delay</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="kpi-section">
                            <div class="kpi-section-title">🏥 Comorbidities & Impact</div>
                            <div class="kpi-grid">
                                <div class="kpi-item" style="border-left-color: #e53e3e;">
                                    <div class="kpi-value">80%</div>
                                    <div class="kpi-label">Have Depression</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #dd6b20;">
                                    <div class="kpi-value">50%</div>
                                    <div class="kpi-label">Substance Abuse Rate</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #38b2ac;">
                                    <div class="kpi-value">25%</div>
                                    <div class="kpi-label">Diabetes Prevalence</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #9f7aea;">
                                    <div class="kpi-value">15-20 yrs</div>
                                    <div class="kpi-label">Reduced Life Expectancy</div>
                                </div>
                            </div>
                        </div>
                    `
                },
                'unmet-need': {
                    title: '🎯 Clinical Unmet Need KPIs',
                    content: `
                        <div class="kpi-description">
                            <p>Assessment of treatment gaps, response rates, and areas where current therapies fall short of patient needs.</p>
                        </div>
                        
                        <div class="kpi-section">
                            <div class="kpi-section-title">⚠️ Treatment Response</div>
                            <div class="kpi-grid">
                                <div class="kpi-item" style="border-left-color: #e53e3e;">
                                    <div class="kpi-value">60%</div>
                                    <div class="kpi-label">Treatment-Resistant Cases</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #dd6b20;">
                                    <div class="kpi-value">40%</div>
                                    <div class="kpi-label">Medication Non-Adherence</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #805ad5;">
                                    <div class="kpi-value">25%</div>
                                    <div class="kpi-label">Adequate Symptom Control</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #38b2ac;">
                                    <div class="kpi-value">5.4/10</div>
                                    <div class="kpi-label">Quality of Life Score</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="kpi-section">
                            <div class="kpi-section-title">🎯 Opportunity Areas</div>
                            <div class="kpi-grid">
                                <div class="kpi-item" style="border-left-color: #3182ce;">
                                    <div class="kpi-value">85%</div>
                                    <div class="kpi-label">Need Cognitive Enhancement</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #38a169;">
                                    <div class="kpi-value">70%</div>
                                    <div class="kpi-label">Negative Symptom Burden</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #d69e2e;">
                                    <div class="kpi-value">90%</div>
                                    <div class="kpi-label">Functional Impairment</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #9f7aea;">
                                    <div class="kpi-value">55%</div>
                                    <div class="kpi-label">Unemployment Rate</div>
                                </div>
                            </div>
                        </div>
                    `
                },
                'competitive-landscape': {
                    title: '⚔️ Competitive Landscape KPIs',
                    content: `
                        <div class="kpi-description">
                            <p>Analysis of competitive intensity, market participants, and strategic positioning within the schizophrenia therapeutic space.</p>
                        </div>
                        
                        <div class="kpi-section">
                            <div class="kpi-section-title">🏢 Market Players</div>
                            <div class="kpi-grid">
                                <div class="kpi-item" style="border-left-color: #e53e3e;">
                                    <div class="kpi-value">45</div>
                                    <div class="kpi-label">Active Sponsors</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #3182ce;">
                                    <div class="kpi-value">78</div>
                                    <div class="kpi-label">Phase II/III Trials</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #38a169;">
                                    <div class="kpi-value">12</div>
                                    <div class="kpi-label">Near-term Launches</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #805ad5;">
                                    <div class="kpi-value">8</div>
                                    <div class="kpi-label">Major Players</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="kpi-section">
                            <div class="kpi-section-title">📊 Competitive Intensity</div>
                            <div class="kpi-grid">
                                <div class="kpi-item" style="border-left-color: #e53e3e;">
                                    <div class="kpi-value">7.3/10</div>
                                    <div class="kpi-label">Risk Score</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #dd6b20;">
                                    <div class="kpi-value">Red Ocean</div>
                                    <div class="kpi-label">Market Status</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #38b2ac;">
                                    <div class="kpi-value">High</div>
                                    <div class="kpi-label">Pricing Pressure</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #9f7aea;">
                                    <div class="kpi-value">65%</div>
                                    <div class="kpi-label">Generic Share</div>
                                </div>
                            </div>
                        </div>
                    `
                }
            };
            
            return kpiData[dimension] || { title: 'KPIs', content: '<p>No data available</p>' };
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('kpiModal');
            if (event.target === modal) {
                closeKPIModal();
            }
        }

        function showSection(sectionName, event) {
            // Hide all content sections
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });
            
            // Remove active state from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Show selected section
            const targetSection = document.getElementById(sectionName + '-section');
            
            if (targetSection) {
                targetSection.classList.add('active');
            } else {
                console.error('Target section not found:', sectionName + '-section');
            }
            
            // Set active state on clicked nav item
            if (event && event.target) {
                event.target.classList.add('active');
            } else {
                // Fallback: find the nav item that matches the section name
                document.querySelectorAll('.nav-item').forEach(item => {
                    const onclick = item.getAttribute('onclick');
                    if (onclick && onclick.includes(`'${sectionName}'`)) {
                        item.classList.add('active');
                    }
                });
            }
        }

        function openChatbot() {
            const chatBtn = document.getElementById('chatbotBtn');
            if (chatBtn.disabled) {
                return; // Don't open if button is disabled
            }
            
            const modal = document.getElementById('chatbotModal');
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
            
            // Focus on input
            setTimeout(() => {
                document.getElementById('chatInput').focus();
            }, 300);
        }

        function closeChatbot() {
            const modal = document.getElementById('chatbotModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            
            if (message === '') return;
            
            // Add user message
            addMessage(message, 'user');
            
            // Clear input
            input.value = '';
            
            // Show typing indicator
            showTypingIndicator();
            
            // Simulate AI response after delay
            setTimeout(() => {
                hideTypingIndicator();
                const response = generateAIResponse(message);
                addMessage(response, 'bot');
            }, 1500);
        }

        function addMessage(content, sender) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const time = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            
            messageDiv.innerHTML = `
                <div class="message-avatar">${sender === 'bot' ? '🤖' : '👤'}</div>
                <div>
                    <div class="message-content">${content}</div>
                    <div class="message-time">${time}</div>
                </div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function showTypingIndicator() {
            const indicator = document.getElementById('typingIndicator');
            indicator.style.display = 'block';
            
            const messagesContainer = document.getElementById('chatMessages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function hideTypingIndicator() {
            const indicator = document.getElementById('typingIndicator');
            indicator.style.display = 'none';
        }

                 function generateAIResponse(question) {
             const responses = {
                 'what\'s the average time between diagnosis and first treatment?': `Based on our claims data analysis, here are the diagnosis-to-treatment timeline insights:<br><br>⏱️ <strong>Average Time to First Treatment</strong>: 23 days<br>📊 <strong>Median Time</strong>: 18 days<br>🎯 <strong>Best Practice Range</strong>: 14-21 days<br><br><strong>Breakdown by Therapy Line</strong>:<br>🥇 First-line therapy: 16 days average<br>🥈 Second-line therapy: 45 days average<br>🥉 Third-line+ therapy: 67 days average<br><br><strong>Regional Variations</strong>:<br>🏙️ Urban areas: 19 days average<br>🌾 Rural areas: 31 days average<br>📍 Northeast: 21 days<br>📍 Southeast: 28 days<br>📍 West: 20 days<br><br>⚠️ <strong>Delays Often Caused By</strong>: Prior authorization (34%), specialist referral wait times (28%), insurance verification (22%)`,

                 'which specialists prescribe the most second-line therapies?': `Based on NPI taxonomy analysis of second-line therapy prescriptions:<br><br>🏆 <strong>Top Second-Line Prescribing Specialties</strong>:<br><br>🎗️ <strong>Medical Oncologists</strong>: 67% of 2L prescriptions<br>• 4,237 unique NPIs<br>• Average 42 patients per prescriber<br>• Highest volume in CA, NY, TX<br><br>🫘 <strong>Urologists</strong>: 23% of 2L prescriptions<br>• 1,894 unique NPIs<br>• Average 28 patients per prescriber<br>• Strong presence in academic centers<br><br>🏥 <strong>Internal Medicine</strong>: 7% of 2L prescriptions<br>• 892 unique NPIs<br>• Average 15 patients per prescriber<br>• Often in community settings<br><br>🩻 <strong>Radiation Oncologists</strong>: 3% of 2L prescriptions<br>• 234 unique NPIs<br>• Average 31 patients per prescriber<br>• Concentrated in major cancer centers<br><br>📈 <strong>Growth Trend</strong>: Medical oncologists increasing 2L share by 8% annually`,

                 'show me the top 5 drug switching patterns': `Top 5 drug switching patterns identified from claims sequence analysis:<br><br>🔄 <strong>Most Common Switching Patterns</strong>:<br><br><strong>1. Pembrolizumab → Atezolizumab</strong><br>📊 28% of all switches<br>⏱️ Average switch time: 8.3 months<br>💡 Driver: Progressive disease<br><br><strong>2. Cisplatin → Carboplatin</strong><br>📊 22% of all switches<br>⏱️ Average switch time: 4.2 months<br>💡 Driver: Toxicity/renal function<br><br><strong>3. Atezolizumab → Pembrolizumab</strong><br>📊 18% of all switches<br>⏱️ Average switch time: 6.7 months<br>💡 Driver: Efficacy concerns<br><br><strong>4. Gemcitabine mono → Combination therapy</strong><br>📊 14% of all switches<br>⏱️ Average switch time: 5.1 months<br>💡 Driver: Disease progression<br><br><strong>5. First-line combo → Maintenance therapy</strong><br>📊 12% of all switches<br>⏱️ Average switch time: 12.4 months<br>💡 Driver: Planned treatment sequence<br><br>🎯 <strong>Key Insight</strong>: 63% of switches occur within 6 months, indicating early treatment optimization`,

                 'which geographic regions have highest per-capita utilization?': `Geographic utilization analysis based on claims per 100K population:<br><br>🌎 <strong>Highest Per-Capita Utilization Regions</strong>:<br><br><strong>Top States:</strong><br>🥇 <strong>Massachusetts</strong>: 847 claims per 100K<br>• High academic medical center density<br>• Strong specialist network<br>• Early adoption of new therapies<br><br>🥈 <strong>Connecticut</strong>: 792 claims per 100K<br>• Proximity to major cancer centers<br>• High insurance coverage rates<br>• Affluent patient population<br><br>🥉 <strong>Maryland</strong>: 763 claims per 100K<br>• NIH/Johns Hopkins influence<br>• Research participation rates<br>• Government employee benefits<br><br><strong>By Region:</strong><br>📍 Northeast: 651 per 100K (highest)<br>📍 West: 598 per 100K<br>📍 Southeast: 542 per 100K<br>📍 Midwest: 487 per 100K<br>📍 Southwest: 434 per 100K (lowest)<br><br>🎯 <strong>Utilization Drivers</strong>: Specialist density (40%), academic centers (28%), insurance coverage (22%), income levels (10%)`
             };

            // Convert to lowercase for matching
            const lowerQuestion = question.toLowerCase();
            
            // Find exact or partial matches
            for (const [key, response] of Object.entries(responses)) {
                if (lowerQuestion.includes(key) || key.includes(lowerQuestion)) {
                    return response;
                }
            }
            
            // Check for keywords
            if (lowerQuestion.includes('time') || lowerQuestion.includes('diagnosis') || lowerQuestion.includes('treatment') || lowerQuestion.includes('average')) {
                return responses['what\'s the average time between diagnosis and first treatment?'];
            } else if (lowerQuestion.includes('specialist') || lowerQuestion.includes('prescrib') || lowerQuestion.includes('second-line') || lowerQuestion.includes('2l')) {
                return responses['which specialists prescribe the most second-line therapies?'];
            } else if (lowerQuestion.includes('switch') || lowerQuestion.includes('pattern') || lowerQuestion.includes('drug') || lowerQuestion.includes('top 5')) {
                return responses['show me the top 5 drug switching patterns'];
            } else if (lowerQuestion.includes('geographic') || lowerQuestion.includes('region') || lowerQuestion.includes('utilization') || lowerQuestion.includes('per-capita')) {
                return responses['which geographic regions have highest per-capita utilization?'];
            }
            
                         // Default response
             return `Thank you for your question! Based on our claims analytics, I can provide insights on:<br><br>⏱️ <strong>Treatment Timelines</strong>: Average 23 days from diagnosis to first treatment<br>🏥 <strong>Prescriber Patterns</strong>: Medical oncologists lead 2L therapy prescribing (67%)<br>🔄 <strong>Drug Switching</strong>: Top pattern is Pembrolizumab → Atezolizumab (28%)<br>🌎 <strong>Geographic Trends</strong>: Massachusetts has highest per-capita utilization (847/100K)<br><br>Try asking about specific areas like "average time to treatment" or "geographic utilization" for detailed insights!`;
        }

        function askSuggestion(question) {
            const input = document.getElementById('chatInput');
            input.value = question;
            sendMessage();
        }

        function handleChatKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        

         // Close modals when clicking outside
         window.onclick = function(event) {
             const kpiModal = document.getElementById('kpiModal');
             const chatModal = document.getElementById('chatbotModal');
             
             if (event.target === kpiModal) {
                 closeKPIModal();
             }
             if (event.target === chatModal) {
                 closeChatbot();
             }
         }

        // Code validation functions
        function removeCode(button) {
            const codeItem = button.closest('.code-item');
            const panel = button.closest('.code-validation-panel');
            const badge = panel.querySelector('.selection-badge');
            
            // Change from selected to suggested
            codeItem.classList.remove('selected');
            codeItem.classList.add('suggested');
            
            // Change button from remove to add
            button.className = 'add-code-btn';
            button.textContent = '+';
            button.onclick = function() { addCode(this); };
            
            // Update badge count
            updateSelectionCount(panel);
        }

        function addCode(button) {
            const codeItem = button.closest('.code-item');
            const panel = button.closest('.code-validation-panel');
            const badge = panel.querySelector('.selection-badge');
            
            // Change from suggested to selected
            codeItem.classList.remove('suggested');
            codeItem.classList.add('selected');
            
            // Change button from add to remove
            button.className = 'remove-code-btn';
            button.textContent = '×';
            button.onclick = function() { removeCode(this); };
            
            // Update badge count
            updateSelectionCount(panel);
        }

        function updateSelectionCount(panel) {
            const selectedCount = panel.querySelectorAll('.code-item.selected').length;
            const badge = panel.querySelector('.selection-badge');
            badge.textContent = `${selectedCount} selected`;
         }

        // Initialize the interface
        document.addEventListener('DOMContentLoaded', function() {
            showStep(1);
        });
    </script>
</body>
</html>
</html>